<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>uni-smpp-accept-service</artifactId>
        <groupId>com.uni.touch</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>uni-smpp-accept-service-service</artifactId>
    <name>uni-smpp-accept-service-service</name>


    <dependencies>

        <dependency>
            <groupId>com.uni.touch</groupId>
            <artifactId>uni-smpp-accept-service-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uni.touch</groupId>
            <artifactId>uni-smpp-accept-service-integration</artifactId>
        </dependency>

        <!-- ch-smpp SMPP协议实现 -->
        <dependency>
            <groupId>com.fizzed</groupId>
            <artifactId>ch-smpp</artifactId>
            <version>5.0.9</version>
        </dependency>

        <!-- ch-commons-charset 字符集工具 -->
        <!--        <dependency>-->
        <!--            <groupId>com.cloudhopper</groupId>-->
        <!--            <artifactId>ch-commons-charset</artifactId>-->
        <!--            <version>3.0.2</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.uni.touch</groupId>
            <artifactId>uni-user-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>9.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.9</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

</project>