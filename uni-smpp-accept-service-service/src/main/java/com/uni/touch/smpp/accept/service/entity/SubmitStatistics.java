package com.uni.touch.smpp.accept.service.entity;

import lombok.Data;

/**
 * 短信提交统计信息实体
 * 用于统计短信提交的成功和失败情况
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class SubmitStatistics {
    
    /**
     * 总提交数
     */
    private Long totalSubmitted;
    
    /**
     * 成功数
     */
    private Long successCount;
    
    /**
     * 失败数
     */
    private Long failureCount;
    
    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalSubmitted == null || totalSubmitted == 0) {
            return 0.0;
        }
        return successCount != null ? (double) successCount / totalSubmitted : 0.0;
    }
    
    /**
     * 计算失败率
     */
    public double getFailureRate() {
        if (totalSubmitted == null || totalSubmitted == 0) {
            return 0.0;
        }
        return failureCount != null ? (double) failureCount / totalSubmitted : 0.0;
    }
}
