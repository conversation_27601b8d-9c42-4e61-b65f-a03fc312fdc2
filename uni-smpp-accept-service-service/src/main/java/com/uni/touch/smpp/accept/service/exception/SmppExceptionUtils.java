package com.uni.touch.smpp.accept.service.exception;

import com.cloudhopper.smpp.type.SmppProcessingException;
import lombok.extern.slf4j.Slf4j;

/**
 * SMPP异常处理工具类
 * 负责将业务异常转换为ch-smpp框架的SmppProcessingException
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
public class SmppExceptionUtils {

    /**
     * 创建SMPP处理异常
     *
     * @param errorInfo 错误信息定义
     * @param args      格式化参数
     * @return SmppProcessingException
     */
    public static SmppProcessingException createSmppException(SmppErrorInfo errorInfo, Object... args) {
        String formattedMessage = errorInfo.getFormattedMessage(args);

        log.warn("创建SMPP异常 - 状态码: {}, 消息: {}",
                errorInfo.getHexStatusCode(), formattedMessage);

        return new SmppProcessingException(errorInfo.getSmppStatusCode(), formattedMessage);
    }

    /**
     * 将通用异常转换为SMPP处理异常
     *
     * @param throwable 原始异常
     * @return SmppProcessingException
     */
    public static SmppProcessingException convertToSmppException(Throwable throwable) {
        if (throwable == null) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "Unknown error");
        }

        // 如果已经是SmppProcessingException，直接返回
        if (throwable instanceof SmppProcessingException) {
            return (SmppProcessingException) throwable;
        }

        // 根据异常类型和消息内容进行智能转换
        String message = throwable.getMessage();
        String className = throwable.getClass().getSimpleName();

        // 处理常见的异常类型
        if (throwable instanceof IllegalArgumentException) {
            return handleIllegalArgumentException(message);
        }

        if (throwable instanceof NullPointerException) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "空指针异常: " + message);
        }

        if (throwable instanceof SecurityException) {
            return createSmppException(SmppErrorInfo.SMPP_BIND_FAILED, "安全异常: " + message);
        }

        if (throwable instanceof java.util.concurrent.TimeoutException) {
            return createSmppException(SmppErrorInfo.SMPP_BIND_TIMEOUT);
        }

        if (throwable instanceof java.net.ConnectException) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "连接异常: " + message);
        }

        if (throwable instanceof java.io.IOException) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "IO异常: " + message);
        }

        // 基于异常消息内容进行智能判断
        if (message != null) {
            String lowerMessage = message.toLowerCase();

            if (lowerMessage.contains("system id") || lowerMessage.contains("systemid")) {
                return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ID_INVALID, message);
            }

            if (lowerMessage.contains("password")) {
                return createSmppException(SmppErrorInfo.SMPP_PASSWORD_INCORRECT, message);
            }

            if (lowerMessage.contains("bind") || lowerMessage.contains("绑定")) {
                return createSmppException(SmppErrorInfo.SMPP_BIND_FAILED, message);
            }

            if (lowerMessage.contains("throttle") || lowerMessage.contains("limit") ||
                    lowerMessage.contains("限流") || lowerMessage.contains("限制")) {
                return createSmppException(SmppErrorInfo.SMPP_SUBMIT_SPEED_EXCEEDED, message);
            }

            if (lowerMessage.contains("source") && lowerMessage.contains("address")) {
                return createSmppException(SmppErrorInfo.SMPP_INVALID_SOURCE_ADDRESS, message);
            }

            if (lowerMessage.contains("dest") && lowerMessage.contains("address")) {
                return createSmppException(SmppErrorInfo.SMPP_INVALID_DEST_ADDRESS, message);
            }

            if (lowerMessage.contains("queue") && lowerMessage.contains("full")) {
                return createSmppException(SmppErrorInfo.SMPP_MESSAGE_QUEUE_FULL);
            }

            if (lowerMessage.contains("encoding") || lowerMessage.contains("coding")) {
                return createSmppException(SmppErrorInfo.SMPP_INVALID_DATA_CODING, message);
            }
        }

        // 默认转换为系统错误
        log.error("未知异常类型，转换为系统错误 - 异常类型: {}, 消息: {}", className, message, throwable);
        return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR,
                String.format("%s: %s", className, message != null ? message : "Unknown"));
    }

    /**
     * 处理IllegalArgumentException
     */
    private static SmppProcessingException handleIllegalArgumentException(String message) {
        if (message == null) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "参数异常");
        }

        String lowerMessage = message.toLowerCase();

        // 根据消息内容判断具体的错误类型
        if (lowerMessage.contains("system id") || lowerMessage.contains("systemid")) {
            return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ID_INVALID, message);
        }

        if (lowerMessage.contains("password")) {
            return createSmppException(SmppErrorInfo.SMPP_PASSWORD_INCORRECT, message);
        }

        if (lowerMessage.contains("source") && lowerMessage.contains("address")) {
            return createSmppException(SmppErrorInfo.SMPP_INVALID_SOURCE_ADDRESS, message);
        }

        if (lowerMessage.contains("dest") && lowerMessage.contains("address")) {
            return createSmppException(SmppErrorInfo.SMPP_INVALID_DEST_ADDRESS, message);
        }

        if (lowerMessage.contains("encoding") || lowerMessage.contains("coding")) {
            return createSmppException(SmppErrorInfo.SMPP_INVALID_DATA_CODING, message);
        }

        // 默认为系统错误
        return createSmppException(SmppErrorInfo.SMPP_SYSTEM_ERROR, "参数异常: " + message);
    }

    /**
     * 安全地处理异常，确保不会抛出新的异常
     */
    public static SmppProcessingException safeConvertToSmppException(Throwable throwable) {
        try {
            return convertToSmppException(throwable);
        } catch (Exception e) {
            log.error("异常转换器本身发生异常", e);
            return new SmppProcessingException(SmppErrorInfo.SMPP_SYSTEM_ERROR.getSmppStatusCode(),
                    "Exception converter error: " + e.getMessage());
        }
    }

    /**
     * 记录异常详情
     */
    public static void logException(String context, Throwable throwable) {
        if (throwable instanceof SmppProcessingException) {
            SmppProcessingException smppException = (SmppProcessingException) throwable;
            log.warn("SMPP异常 [{}]: 0x{} - {}", context,
                    Integer.toHexString(smppException.getErrorCode()).toUpperCase(),
                    smppException.getMessage());
        } else {
            log.error("系统异常 [{}]: {}", context, throwable.getMessage(), throwable);
        }
    }
}
