package com.uni.touch.smpp.accept.service.converter;

import com.cloudhopper.commons.charset.Charset;
import com.cloudhopper.commons.charset.CharsetUtil;
import com.cloudhopper.smpp.SmppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SMPP编码转换器
 * 基于ch-smpp框架的CharsetUtil进行正确的编码转换
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppEncodingConverter {

    /**
     * smppEncoding到dataCoding的正确映射
     * 基于SMPP协议标准和CharsetUtil常量
     */
    public byte convertToDataCoding(Integer smppEncoding) {
        if (smppEncoding == null) {
            return SmppConstants.DATA_CODING_DEFAULT;
        }

        switch (smppEncoding) {
            case 1:
                return SmppConstants.DATA_CODING_DEFAULT;  // GSM 7-bit (0x00)
            case 2:
                return SmppConstants.DATA_CODING_IA5;      // ASCII/IA5 (0x01)
            case 3:
                return SmppConstants.DATA_CODING_LATIN1;   // Latin-1/ISO-8859-1 (0x03)
            case 4:
                return SmppConstants.DATA_CODING_UCS2;     // UCS2 (0x08)
            case 5:
                return SmppConstants.DATA_CODING_DEFAULT;  // UTF-8 (使用默认编码处理)
            default:
                log.warn("不支持的smppEncoding: {}, 使用默认编码", smppEncoding);
                return SmppConstants.DATA_CODING_DEFAULT;
        }
    }

    /**
     * smppEncoding到Charset的映射（使用CharsetUtil的正确常量）
     */
    public Charset getCharset(Integer smppEncoding) {
        if (smppEncoding == null || smppEncoding == 1) {
            return CharsetUtil.CHARSET_GSM;        // GSM 7-bit
        } else if (smppEncoding == 2) {
            return CharsetUtil.CHARSET_ISO_8859_1; // ASCII/IA5 (ISO-8859-1)
        } else if (smppEncoding == 3) {
            return CharsetUtil.CHARSET_ISO_8859_1; // Latin-1 (ISO-8859-1)
        } else if (smppEncoding == 4) {
            return CharsetUtil.CHARSET_UCS_2;      // UCS2 (Unicode)
        } else if (smppEncoding == 5) {
            return CharsetUtil.CHARSET_UTF_8;      // UTF-8
        } else {
            return CharsetUtil.CHARSET_GSM;        // 默认GSM 7-bit
        }
    }

    /**
     * 根据编码转换消息内容为字节数组（使用CharsetUtil）
     */
    public byte[] encodeMessage(String content, Integer smppEncoding) {
        if (content == null || content.isEmpty()) {
            return new byte[0];
        }

        try {
            Charset charset = getCharset(smppEncoding);
            return CharsetUtil.encode(content, charset);
        } catch (Exception e) {
            log.error("编码消息内容异常 - content: {}, encoding: {}", content, smppEncoding, e);
            return CharsetUtil.encode(content, CharsetUtil.CHARSET_GSM);
        }
    }

    /**
     * 根据编码解码消息内容（使用CharsetUtil）
     */
    public String decodeMessage(byte[] messageBytes, Integer smppEncoding) {
        if (messageBytes == null || messageBytes.length == 0) {
            return "";
        }

        try {
            Charset charset = getCharset(smppEncoding);
            return CharsetUtil.decode(messageBytes, charset);
        } catch (Exception e) {
            log.error("解码消息内容异常 - encoding: {}", smppEncoding, e);
            return CharsetUtil.decode(messageBytes, CharsetUtil.CHARSET_GSM);
        }
    }

    /**
     * 根据dataCoding解码消息内容
     * 基于SMPP协议标准和CharsetUtil常量
     */
    public String decodeMessage(byte[] messageBytes, byte dataCoding) {
        if (messageBytes == null || messageBytes.length == 0) {
            return "";
        }

        try {
            Charset charset;
            switch (dataCoding) {
                case SmppConstants.DATA_CODING_DEFAULT:
                    charset = CharsetUtil.CHARSET_GSM;        // GSM 7-bit
                    break;
                case SmppConstants.DATA_CODING_IA5:
                    charset = CharsetUtil.CHARSET_ISO_8859_1; // ASCII/IA5
                    break;
                case SmppConstants.DATA_CODING_LATIN1:
                    charset = CharsetUtil.CHARSET_ISO_8859_1; // Latin-1/ISO-8859-1
                    break;
                case SmppConstants.DATA_CODING_UCS2:
                    charset = CharsetUtil.CHARSET_UCS_2;      // UCS2
                    break;
                case SmppConstants.DATA_CODING_8BIT:
                    charset = CharsetUtil.CHARSET_ISO_8859_1; // 8-bit binary
                    break;
                default:
                    log.warn("不支持的dataCoding: 0x{}, 使用GSM编码", Integer.toHexString(dataCoding));
                    charset = CharsetUtil.CHARSET_GSM;
            }

            return CharsetUtil.decode(messageBytes, charset);
        } catch (Exception e) {
            log.error("解码消息内容异常 - dataCoding: 0x{}", Integer.toHexString(dataCoding), e);
            return CharsetUtil.decode(messageBytes, CharsetUtil.CHARSET_GSM);
        }
    }

    /**
     * 检查消息是否为长短信（基于编码类型和内容长度）
     */
    public boolean isLongMessage(byte[] messageBytes, Integer smppEncoding) {
        if (messageBytes == null) {
            return false;
        }

        String content = decodeMessage(messageBytes, smppEncoding);
        return isLongMessage(content, smppEncoding);
    }

    /**
     * 检查消息是否为长短信（基于内容长度和编码类型）
     */
    public boolean isLongMessage(String content, Integer smppEncoding) {
        if (content == null) {
            return false;
        }

        // 根据编码类型判断长短信阈值
        int threshold;
        if (smppEncoding == null || smppEncoding == 1) {
            // GSM 7-bit编码，单条短信最大160字符
            threshold = 160;
        } else if (smppEncoding == 4) {
            // UCS2编码，单条短信最大70字符
            threshold = 70;
        } else if (smppEncoding == 5) {
            // UTF-8编码，按字节计算，约140字节
            return CharsetUtil.encode(content, CharsetUtil.CHARSET_UTF_8).length > 140;
        } else {
            // ASCII/Latin-1编码，单条短信最大160字符
            threshold = 160;
        }

        return content.length() > threshold;
    }

    /**
     * 获取编码类型描述
     */
    public String getEncodingDescription(Integer smppEncoding) {
        if (smppEncoding == null) {
            return "GSM 7-bit (默认)";
        }

        switch (smppEncoding) {
            case 1:
                return "GSM 7-bit";
            case 2:
                return "ASCII (IA5)";
            case 3:
                return "Latin-1 (ISO-8859-1)";
            case 4:
                return "UCS2 (Unicode)";
            case 5:
                return "UTF-8";
            default:
                return "未知编码(" + smppEncoding + ")";
        }
    }

    /**
     * 获取单条短信最大字符数
     */
    public int getMaxSingleSmsLength(Integer smppEncoding) {
        if (smppEncoding == null || smppEncoding == 1) {
            return 160; // GSM 7-bit
        } else if (smppEncoding == 4) {
            return 70;  // UCS2
        } else if (smppEncoding == 5) {
            return 140; // UTF-8 (按字节计算)
        } else {
            return 160; // ASCII/Latin-1
        }
    }

    /**
     * 获取长短信最大字符数（约10条短信）
     */
    public int getMaxLongSmsLength(Integer smppEncoding) {
        return getMaxSingleSmsLength(smppEncoding) * 10;
    }

    /**
     * 根据CharsetUtil常量名称获取Charset
     */
    public Charset getCharsetByName(String charsetName) {
        switch (charsetName) {
            case CharsetUtil.NAME_GSM:
                return CharsetUtil.CHARSET_GSM;
            case CharsetUtil.NAME_ISO_8859_1:
                return CharsetUtil.CHARSET_ISO_8859_1;
            case CharsetUtil.NAME_UCS_2:
                return CharsetUtil.CHARSET_UCS_2;
            case CharsetUtil.NAME_UTF_8:
                return CharsetUtil.CHARSET_UTF_8;
            case CharsetUtil.NAME_PACKED_GSM:
                return CharsetUtil.CHARSET_PACKED_GSM;
            default:
                log.warn("不支持的字符集名称: {}, 使用GSM编码", charsetName);
                return CharsetUtil.CHARSET_GSM;
        }
    }

    /**
     * 获取所有支持的编码类型
     */
    public String[] getSupportedEncodings() {
        return new String[]{
            "1 - GSM 7-bit",
            "2 - ASCII (IA5)",
            "3 - Latin-1 (ISO-8859-1)",
            "4 - UCS2 (Unicode)",
            "5 - UTF-8"
        };
    }
}
