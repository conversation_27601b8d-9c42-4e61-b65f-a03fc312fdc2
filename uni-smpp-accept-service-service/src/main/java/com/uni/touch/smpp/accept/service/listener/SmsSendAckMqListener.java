package com.uni.touch.smpp.accept.service.listener;

import com.alibaba.fastjson2.TypeReference;
import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.SmppServerSession;
import com.cloudhopper.smpp.pdu.DeliverSm;
import com.cloudhopper.smpp.tlv.Tlv;
import com.cloudhopper.smpp.type.Address;
import com.cloudhopper.smpp.util.DeliveryReceipt;
import com.google.common.util.concurrent.RateLimiter;
import com.uni.touch.boot.mq.AbstractMqListener;
import com.uni.touch.smpp.accept.service.entity.SmppAccount;
import com.uni.touch.smpp.accept.service.limiter.SmppRateLimiter;
import com.uni.touch.smpp.accept.service.manager.SmppSessionManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 短信发送的回执消息处理
 *
 * <AUTHOR>
 * @date 2025/07/24
 */
@Slf4j
@Service
public class SmsSendAckMqListener extends AbstractMqListener<Object> {

    @Autowired
    private SmppSessionManager sessionManager;

    @Autowired
    private SmppRateLimiter rateLimiter;

    @Value("${rocketmq.sms.ack.topic}")
    private String TOPIC;

    @Value("${rocketmq.sms.ack.tag}")
    private String TAG;

    @Override
    protected ConsumeResult handleMessage(Object o, MessageView messageView) {
        try {
            log.info("收到短信回执MQ消息 - messageId: {}, topic: {}, tag: {}",
                    messageView.getMessageId(), messageView.getTopic(), messageView.getTag());

            // 1. 模拟解析MQ消息体（后续替换为真实解析）
            SmsDeliveryInfo deliveryInfo = parseDeliveryInfo(messageView);

            // 2. 根据业务标识查找对应的SMPP会话
            List<SmppServerSession> targetSessions = findTargetSessions(deliveryInfo);

            // 3. 为每个会话推送状态报告
            for (SmppServerSession session : targetSessions) {
                pushDeliveryReceipt(session, deliveryInfo);
            }

            return ConsumeResult.SUCCESS;

        } catch (Exception e) {
            log.error("处理短信回执MQ消息失败 - messageId: {}, error: {}",
                    messageView.getMessageId(), e.getMessage(), e);
            return ConsumeResult.FAILURE;
        }
    }

    @Override
    protected Type getReflectType() {
        return new TypeReference<Object>() {
        }.getType();
    }

    @Override
    public String getTopic() {
        return TOPIC;
    }

    @Override
    public String getTag() {
        return TAG;
    }

    /**
     * 解析短信回执信息（模拟数据，后续替换）
     */
    private SmsDeliveryInfo parseDeliveryInfo(MessageView messageView) {
        // TODO: 根据实际MQ消息格式解析，这里先模拟数据
        SmsDeliveryInfo info = new SmsDeliveryInfo();

        // 模拟基础信息
        info.setMessageId("MSG" + System.currentTimeMillis());
        info.setPhoneNumber("13800138000");
        info.setSystemId("TEST_SYSTEM");
        info.setOutId("OUT" + System.currentTimeMillis());

        // 模拟状态信息
        info.setDeliveryState(SmppConstants.STATE_DELIVERED);
        info.setErrorCode("000");
        info.setSubmitTime(new DateTime().minusMinutes(1));
        info.setDoneTime(new DateTime());

        // 模拟原始短信内容（前20个字符）
        info.setOriginalText("Hello World");

        log.info("解析短信回执信息 - messageId: {}, phone: {}, state: {}",
                info.getMessageId(), info.getPhoneNumber(), info.getDeliveryState());

        return info;
    }

    /**
     * 查找目标SMPP会话
     */
    private List<SmppServerSession> findTargetSessions(SmsDeliveryInfo deliveryInfo) {
        List<SmppServerSession> sessions = new ArrayList<>();

        // TODO: 根据实际业务逻辑查找会话
        // 可能的查找方式：
        // 1. 根据systemId查找
        // 2. 根据messageId查找（需要在提交时建立映射）
        // 3. 根据outId查找

        // 这里先模拟查找逻辑
        SmppServerSession session = sessionManager.findSessionBySystemId(deliveryInfo.getSystemId());
        if (session != null && session.isBound()) {
            sessions.add(session);
        }

        log.info("找到目标会话数量: {} - systemId: {}", sessions.size(), deliveryInfo.getSystemId());
        return sessions;
    }

    /**
     * 推送状态报告到SMPP会话
     */
    private void pushDeliveryReceipt(SmppServerSession session, SmsDeliveryInfo deliveryInfo) {
        try {
            String systemId = session.getConfiguration().getSystemId();

            // 1. 流控检查
            SmppAccount account = sessionManager.getAccountBySystemId(systemId);
            if (account != null) {
                RateLimiter dlrLimiter = rateLimiter.getDlrRateLimiter(systemId, account.getMaxDlrSpeed());
                if (!dlrLimiter.tryAcquire(1, 100, TimeUnit.MILLISECONDS)) {
                    log.warn("状态报告推送速度超限 - systemId: {}", systemId);
                    return;
                }
            }

            // 2. 构造DeliverSm状态报告
            DeliverSm deliverSm = buildDeliverSm(deliveryInfo, systemId);

            // 3. 异步推送状态报告
            session.sendRequestPdu(deliverSm, 30000, false);

            log.info("推送状态报告成功 - sessionId: {}, messageId: {}, phone: {}, state: {}",
                    session.getConfiguration().getName(), deliveryInfo.getMessageId(),
                    deliveryInfo.getPhoneNumber(), deliveryInfo.getDeliveryState());

        } catch (Exception e) {
            log.error("推送状态报告异常 - sessionId: {}, messageId: {}",
                    session.getConfiguration().getName(), deliveryInfo.getMessageId(), e);
        }
    }

    /**
     * 构造DeliverSm状态报告（基于ch-smpp框架标准）
     */
    private DeliverSm buildDeliverSm(SmsDeliveryInfo deliveryInfo, String systemId) throws Exception {
        DeliverSm deliverSm = new DeliverSm();

        // 1. 设置地址信息
        deliverSm.setSourceAddress(new Address((byte)1, (byte)1, deliveryInfo.getPhoneNumber()));
        deliverSm.setDestAddress(new Address((byte)0, (byte)0, systemId));

        // 2. 设置ESM Class为状态报告
        deliverSm.setEsmClass(SmppConstants.ESM_CLASS_MT_SMSC_DELIVERY_RECEIPT);

        // 3. 设置数据编码
        deliverSm.setDataCoding(SmppConstants.DATA_CODING_DEFAULT);

        // 4. 构造DeliveryReceipt标准格式内容
        DeliveryReceipt receipt = new DeliveryReceipt();
        receipt.setMessageId(deliveryInfo.getMessageId());
        receipt.setState(deliveryInfo.getDeliveryState());
        receipt.setSubmitCount(1);
        receipt.setDeliveredCount(deliveryInfo.getDeliveryState() == SmppConstants.STATE_DELIVERED ? 1 : 0);
        receipt.setSubmitDate(deliveryInfo.getSubmitTime());
        receipt.setDoneDate(deliveryInfo.getDoneTime());
        receipt.setErrorCode(Integer.parseInt(deliveryInfo.getErrorCode()));
        receipt.setText(deliveryInfo.getOriginalText());

        // 5. 生成标准格式的状态报告文本
        String receiptText = receipt.toShortMessage();
        deliverSm.setShortMessage(receiptText.getBytes("ISO-8859-1"));

        // 6. 设置可选参数
        deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_RECEIPTED_MSG_ID, deliveryInfo.getMessageId().getBytes()));
        deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_MSG_STATE, new byte[]{deliveryInfo.getDeliveryState()}));

        return deliverSm;
    }

    /**
     * 短信回执信息模型（模拟数据结构）
     */
    private static class SmsDeliveryInfo {
        private String messageId;        // 消息ID
        private String phoneNumber;      // 手机号码
        private String systemId;        // 系统ID
        private String outId;           // 外部ID
        private byte deliveryState;     // 投递状态
        private String errorCode;       // 错误码
        private DateTime submitTime;    // 提交时间
        private DateTime doneTime;      // 完成时间
        private String originalText;    // 原始短信内容

        // getter/setter方法
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }

        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

        public String getSystemId() { return systemId; }
        public void setSystemId(String systemId) { this.systemId = systemId; }

        public String getOutId() { return outId; }
        public void setOutId(String outId) { this.outId = outId; }

        public byte getDeliveryState() { return deliveryState; }
        public void setDeliveryState(byte deliveryState) { this.deliveryState = deliveryState; }

        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }

        public DateTime getSubmitTime() { return submitTime; }
        public void setSubmitTime(DateTime submitTime) { this.submitTime = submitTime; }

        public DateTime getDoneTime() { return doneTime; }
        public void setDoneTime(DateTime doneTime) { this.doneTime = doneTime; }

        public String getOriginalText() { return originalText; }
        public void setOriginalText(String originalText) { this.originalText = originalText; }
    }
}
