package com.uni.touch.smpp.accept.service.handler;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.SmppServerSession;
import com.cloudhopper.smpp.SmppSessionConfiguration;
import com.cloudhopper.smpp.pdu.BaseBind;
import com.cloudhopper.smpp.pdu.BaseBindResp;
import com.cloudhopper.smpp.type.SmppProcessingException;
import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.smpp.accept.common.config.SmppServerConfig;
import com.uni.touch.smpp.accept.service.entity.SmppAccount;
import com.uni.touch.smpp.accept.integration.user.UniAppDubboService;
import com.uni.touch.smpp.accept.service.exception.SmppErrorInfo;
import com.uni.touch.smpp.accept.service.exception.SmppExceptionUtils;
import com.uni.touch.smpp.accept.service.manager.SmppSessionManager;
import com.uni.touch.smpp.accept.service.manager.SmppSegmentManager;
import com.uni.touch.smpp.accept.service.limiter.SmppRateLimiter;
import com.uni.touch.smpp.accept.service.service.SmppSubmitService;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import com.uni.touch.smpp.accept.service.utils.SmppAssertUtils;
import com.uni.touch.user.api.response.AppInfoResponse;
import com.uni.touch.user.client.cache.SmppInfoCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * SMPP服务器处理器
 * 基于ch-smpp框架，简化设计，直接处理绑定逻辑
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppServerHandler implements com.cloudhopper.smpp.SmppServerHandler {

    @Autowired
    private UniAppDubboService uniAppDubboService;

    @Autowired
    private SmppInfoCache smppInfoCache;

    @Autowired
    private SmppSessionManager sessionManager;

    @Autowired
    private SmppSegmentManager segmentManager;

    @Autowired
    private SmppRateLimiter rateLimiter;

    @Autowired
    private SmppSubmitService submitService;

    @Autowired
    private SmppEncodingConverter encodingConverter;

    @Override
    public void sessionBindRequested(Long sessionId, SmppSessionConfiguration sessionConfiguration,
                                     BaseBind bindRequest) throws SmppProcessingException {
        try {
            String systemId = bindRequest.getSystemId();
            String password = bindRequest.getPassword();

            log.info("收到SMPP绑定请求 - sessionId: {}, systemId: {}, bindType: {}",
                    sessionId, systemId, bindRequest.getName());

            // 1. 参数校验
            SmppAssertUtils.isTrue(StringUtils.isNotBlank(systemId), SmppErrorInfo.SMPP_SYSTEM_ID_REQUIRED);
            SmppAssertUtils.isTrue(StringUtils.isNotBlank(password), SmppErrorInfo.SMPP_PASSWORD_REQUIRED);

            // 2. 账号认证（直接调用Dubbo）
            BaseResult<AppInfoResponse> result = uniAppDubboService.getAppSmppInfoBySmppId(systemId);
            SmppAssertUtils.isTrue(result != null && result.isSuccess(),
                    SmppErrorInfo.SMPP_ACCOUNT_NOT_FOUND, systemId);

            AppInfoResponse appInfo = result.getData();
            SmppAssertUtils.isTrue(appInfo != null, SmppErrorInfo.SMPP_ACCOUNT_NOT_FOUND, systemId);
            SmppAssertUtils.isTrue(Objects.equals(password, appInfo.getSmppPassword()),
                    SmppErrorInfo.SMPP_PASSWORD_INCORRECT, systemId);

            // 3. 转换为SMPP账号对象
            SmppAccount account = SmppAccount.fromAppInfo(appInfo);

            // 4. 检查连接数限制（基于smppMaxLink）
            boolean acquired = sessionManager.tryAcquireConnection(systemId, account.getMaxConnections());
            SmppAssertUtils.isTrue(acquired, SmppErrorInfo.SMPP_MAX_CONNECTIONS_EXCEEDED,
                    systemId, String.valueOf(account.getMaxConnections()));

            // 5. 配置会话参数（基于smppMaxSlippingWindowNumber）
            configureSession(sessionConfiguration, account);

            log.info("SMPP绑定请求验证成功 - sessionId: {}, systemId: {}, windowSize: {}, maxConnections: {}",
                    sessionId, systemId, sessionConfiguration.getWindowSize(), account.getMaxConnections());

        } catch (SmppProcessingException e) {
            throw e;
        } catch (Exception e) {
            throw SmppExceptionUtils.safeConvertToSmppException(e);
        }
    }

    @Override
    public void sessionCreated(Long sessionId, SmppServerSession session,
                               BaseBindResp preparedBindResponse) throws SmppProcessingException {
        try {
            String systemId = session.getConfiguration().getSystemId();

            log.info("SMPP会话创建 - sessionId: {}, systemId: {}, bindType: {}",
                    sessionId, systemId, session.getBindType());

            // 重新获取账号信息（因为sessionCreated在sessionBindRequested之后调用）
            BaseResult<AppInfoResponse> result = uniAppDubboService.getAppSmppInfoBySmppId(systemId);
            SmppAssertUtils.isTrue(result != null && result.isSuccess() && result.getData() != null,
                    SmppErrorInfo.SMPP_ACCOUNT_NOT_FOUND, systemId);

            SmppAccount account = SmppAccount.fromAppInfo(result.getData());

            // 注册会话
            sessionManager.registerSession(sessionId, session, account);

            // 创建新的会话处理器
            SmppSessionHandler sessionHandler = new SmppSessionHandler(sessionId, account,
                    segmentManager, rateLimiter, submitService, encodingConverter);

            // 激活会话
            session.serverReady(sessionHandler);

            log.info("SMPP会话创建完成 - sessionId: {}, systemId: {}", sessionId, systemId);

        } catch (Exception e) {
            // 清理资源
            sessionManager.removeSession(sessionId);
            throw SmppExceptionUtils.safeConvertToSmppException(e);
        }
    }

    @Override
    public void sessionDestroyed(Long sessionId, SmppServerSession session) {
        try {
            String systemId = session != null ? session.getConfiguration().getSystemId() : "未知";

            log.info("SMPP会话销毁 - sessionId: {}, systemId: {}", sessionId, systemId);

            // 移除会话并释放连接许可
            sessionManager.removeSession(sessionId);

            log.info("SMPP会话销毁完成 - sessionId: {}, systemId: {}", sessionId, systemId);

        } catch (Exception e) {
            log.error("SMPP会话销毁异常 - sessionId: {}", sessionId, e);
        }
    }

    /**
     * 配置会话参数
     */
    private void configureSession(SmppSessionConfiguration sessionConfig, SmppAccount account) {

        // 设置窗口大小（基于smppMaxSlippingWindowNumber）
        Integer windowSize = account.getWindowSize();
        if (windowSize != null && windowSize > 0) {
            sessionConfig.setWindowSize(Math.min(windowSize, 100));
        } else {
            sessionConfig.setWindowSize(SmppConstants.DEFAULT_WINDOW_SIZE);
        }

        // 动态计算超时参数
        long windowWaitTimeout = calculateWindowWaitTimeout(account);
        long requestExpiryTimeout = calculateRequestExpiryTimeout(account);
        long windowMonitorInterval = calculateWindowMonitorInterval(account);

        sessionConfig.setWindowWaitTimeout(windowWaitTimeout);
        sessionConfig.setRequestExpiryTimeout(requestExpiryTimeout);
        sessionConfig.setWindowMonitorInterval(windowMonitorInterval);

        // 设置其他参数
        sessionConfig.setBindTimeout(SmppServerConfig.BIND_TIMEOUT);
        sessionConfig.setInterfaceVersion(SmppServerConfig.INTERFACE_VERSION);
        sessionConfig.setCountersEnabled(true);

        // 设置会话名称
        String sessionName = String.format("SMPP-%s", account.getSystemId());
        sessionConfig.setName(sessionName);
    }

    private long calculateWindowWaitTimeout(SmppAccount account) {
        long baseTimeout = 30000L; // 30秒

        if (account.getMaxConnections() != null && account.getMaxConnections() > 1) {
            baseTimeout += (account.getMaxConnections() - 1) * 2000L;
        }

        if (account.getWindowSize() != null && account.getWindowSize() > 5) {
            baseTimeout += (account.getWindowSize() - 5) * 1000L;
        }

        return Math.min(baseTimeout, 120000L);
    }

    private long calculateRequestExpiryTimeout(SmppAccount account) {
        return calculateWindowWaitTimeout(account);
    }

    private long calculateWindowMonitorInterval(SmppAccount account) {
        long baseInterval = 15000L;

        if (account.getMaxConnections() != null && account.getMaxConnections() > 1) {
            baseInterval = Math.max(5000L, baseInterval - (account.getMaxConnections() - 1) * 1000L);
        }

        return baseInterval;
    }
}
