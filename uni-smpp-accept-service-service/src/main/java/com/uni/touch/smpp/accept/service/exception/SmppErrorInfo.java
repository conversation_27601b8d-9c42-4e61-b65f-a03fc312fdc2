package com.uni.touch.smpp.accept.service.exception;

import com.cloudhopper.smpp.SmppConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SMPP错误信息定义
 * 直接映射到SMPP标准状态码，确保与ch-smpp框架完全兼容
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Getter
@AllArgsConstructor
public enum SmppErrorInfo {

    // ==================== 系统ID和密码相关 ====================
    
    /**
     * 系统ID不能为空或无效
     * 映射到 SMPP STATUS_INVSYSID (0x0000000F)
     */
    SMPP_SYSTEM_ID_REQUIRED(SmppConstants.STATUS_INVSYSID, "系统ID不能为空"),
    SMPP_SYSTEM_ID_INVALID(SmppConstants.STATUS_INVSYSID, "系统ID无效：%s"),
    SMPP_ACCOUNT_NOT_FOUND(SmppConstants.STATUS_INVSYSID, "SMPP账号不存在：%s"),
    
    /**
     * 密码相关错误
     * 映射到 SMPP STATUS_INVPASWD (0x0000000E)
     */
    SMPP_PASSWORD_REQUIRED(SmppConstants.STATUS_INVPASWD, "密码不能为空"),
    SMPP_PASSWORD_INCORRECT(SmppConstants.STATUS_INVPASWD, "密码错误：%s"),
    
    // ==================== 连接和限流相关 ====================
    
    /**
     * 连接数超限
     * 映射到 SMPP STATUS_THROTTLED (0x00000058)
     */
    SMPP_MAX_CONNECTIONS_EXCEEDED(SmppConstants.STATUS_THROTTLED, "连接数超过限制：当前%s，最大%s"),
    
    /**
     * 提交速度超限
     * 映射到 SMPP STATUS_THROTTLED (0x00000058)
     */
    SMPP_SUBMIT_SPEED_EXCEEDED(SmppConstants.STATUS_THROTTLED, "提交速度超过限制：%s次/秒"),
    
    /**
     * 状态报告速度超限
     * 映射到 SMPP STATUS_THROTTLED (0x00000058)
     */
    SMPP_DLR_SPEED_EXCEEDED(SmppConstants.STATUS_THROTTLED, "状态报告速度超过限制：%s次/秒"),
    
    /**
     * 窗口大小超限
     * 映射到 SMPP STATUS_THROTTLED (0x00000058)
     */
    SMPP_WINDOW_SIZE_EXCEEDED(SmppConstants.STATUS_THROTTLED, "窗口大小超过限制：%s"),
    
    // ==================== 地址相关 ====================
    
    /**
     * 源地址相关错误
     * 映射到 SMPP STATUS_INVSRCADR (0x0000000A)
     */
    SMPP_SOURCE_ADDRESS_REQUIRED(SmppConstants.STATUS_INVSRCADR, "源地址不能为空"),
    SMPP_INVALID_SOURCE_ADDRESS(SmppConstants.STATUS_INVSRCADR, "源地址无效：%s"),
    
    /**
     * 目标地址相关错误
     * 映射到 SMPP STATUS_INVDSTADR (0x0000000B)
     */
    SMPP_DEST_ADDRESS_REQUIRED(SmppConstants.STATUS_INVDSTADR, "目标地址不能为空"),
    SMPP_INVALID_DEST_ADDRESS(SmppConstants.STATUS_INVDSTADR, "目标地址无效：%s"),
    SMPP_INVALID_ADDRESS_FORMAT(SmppConstants.STATUS_INVDSTADR, "地址格式无效：%s"),
    
    // ==================== 消息内容相关 ====================
    
    /**
     * 消息内容相关错误
     * 映射到 SMPP STATUS_SYSERR (0x00000008)
     */
    SMPP_MESSAGE_CONTENT_REQUIRED(SmppConstants.STATUS_SYSERR, "消息内容不能为空"),
    SMPP_MESSAGE_TOO_LONG(SmppConstants.STATUS_SYSERR, "消息内容过长：当前%s字节，最大%s字节"),
    
    // ==================== 编码相关 ====================
    
    /**
     * 编码相关错误
     * 映射到 SMPP STATUS_INVDCS (0x00000104)
     */
    SMPP_ENCODING_REQUIRED(SmppConstants.STATUS_INVDCS, "编码不能为空"),
    SMPP_INVALID_DATA_CODING(SmppConstants.STATUS_INVDCS, "数据编码无效：%s"),
    INVALID_SMPP_ENCODING(SmppConstants.STATUS_INVDCS, "无效的SMPP编码：%s"),
    
    // ==================== 会话状态相关 ====================
    
    /**
     * 会话状态相关错误
     * 映射到 SMPP STATUS_INVBNDSTS (0x00000004)
     */
    SMPP_SESSION_NOT_BOUND(SmppConstants.STATUS_INVBNDSTS, "会话未绑定"),
    SMPP_INCORRECT_BIND_STATUS(SmppConstants.STATUS_INVBNDSTS, "绑定状态不正确：当前%s，期望%s"),
    
    /**
     * 已绑定状态错误
     * 映射到 SMPP STATUS_ALYBND (0x00000005)
     */
    SMPP_ALREADY_BOUND(SmppConstants.STATUS_ALYBND, "会话已绑定：%s"),
    
    // ==================== 绑定相关 ====================
    
    /**
     * 绑定失败
     * 映射到 SMPP STATUS_BINDFAIL (0x0000000D)
     */
    SMPP_BIND_FAILED(SmppConstants.STATUS_BINDFAIL, "绑定失败：%s"),
    SMPP_BIND_TIMEOUT(SmppConstants.STATUS_BINDFAIL, "绑定超时"),
    
    // ==================== 消息队列相关 ====================
    
    /**
     * 消息队列已满
     * 映射到 SMPP STATUS_MSGQFUL (0x00000014)
     */
    SMPP_MESSAGE_QUEUE_FULL(SmppConstants.STATUS_MSGQFUL, "消息队列已满"),
    
    // ==================== 提交相关 ====================
    
    /**
     * 提交失败
     * 映射到 SMPP STATUS_SUBMITFAIL (0x00000045)
     */
    SMPP_SUBMIT_SM_FAILED(SmppConstants.STATUS_SUBMITFAIL, "提交短信失败：%s"),
    
    // ==================== 服务器相关 ====================
    
    /**
     * 服务器相关错误
     * 映射到 SMPP STATUS_SYSERR (0x00000008)
     */
    SMPP_SERVER_START_FAILED(SmppConstants.STATUS_SYSERR, "服务器启动失败：%s"),
    SMPP_SERVER_STOP_FAILED(SmppConstants.STATUS_SYSERR, "服务器停止失败：%s"),
    SMPP_SSL_CONFIG_ERROR(SmppConstants.STATUS_SYSERR, "SSL配置错误：%s"),
    
    // ==================== 系统错误 ====================
    
    /**
     * 通用系统错误
     * 映射到 SMPP STATUS_SYSERR (0x00000008)
     */
    SMPP_SYSTEM_ERROR(SmppConstants.STATUS_SYSERR, "系统错误：%s");

    /**
     * SMPP标准状态码
     */
    private final int smppStatusCode;
    
    /**
     * 错误消息模板
     */
    private final String message;

    /**
     * 获取格式化的错误消息
     *
     * @param args 格式化参数
     * @return 格式化后的错误消息
     */
    public String getFormattedMessage(Object... args) {
        if (args != null && args.length > 0) {
            return String.format(message, args);
        }
        return message;
    }

    /**
     * 获取十六进制格式的状态码
     *
     * @return 十六进制状态码字符串
     */
    public String getHexStatusCode() {
        return String.format("0x%08X", smppStatusCode);
    }

    @Override
    public String toString() {
        return String.format("SmppErrorInfo{code=%s, message='%s'}", 
                getHexStatusCode(), message);
    }
}
