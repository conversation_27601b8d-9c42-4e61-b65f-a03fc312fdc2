package com.uni.touch.smpp.accept.service.service;

import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.accept.api.request.UniSendSmsRequest;
import com.uni.touch.accept.api.response.UniSendSmsResponse;
import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.smpp.accept.integration.accept.UniSendSmsDubboService;
import com.uni.touch.smpp.accept.service.entity.SmppAccount;
import com.uni.touch.smpp.accept.service.entity.SubmitStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * SMPP短信提交业务服务
 * 处理短信提交的业务逻辑
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Service
public class SmppSubmitService {

    @Autowired
    private UniSendSmsDubboService uniSendSmsDubboService;

    /**
     * 提交短信消息
     */
    public String submitMessage(SubmitSm submitSm, String message, SmppAccount account) {
        try {
            log.info("开始提交短信 - systemId: {}, from: {}, to: {}, length: {}", 
                    account.getSystemId(), 
                    submitSm.getSourceAddress().getAddress(),
                    submitSm.getDestAddress().getAddress(),
                    message.length());

            // 构造请求参数
            UniSendSmsRequest request = buildSendSmsRequest(submitSm, message, account);
            
            // 调用Dubbo接口
            BaseResult<UniSendSmsResponse> result = uniSendSmsDubboService.sendSms(request);
            
            if (result != null && result.isSuccess() && result.getData() != null) {
                // TODO: 根据实际响应字段调整，这里先模拟
                String messageId = generateMessageId();
                
                log.info("短信提交成功 - systemId: {}, messageId: {}, phone: {}, length: {}", 
                        account.getSystemId(), messageId, 
                        submitSm.getDestAddress().getAddress(), message.length());
                
                return messageId;
            } else {
                log.warn("短信提交失败 - systemId: {}, result: {}", account.getSystemId(), result);
                return null;
            }
            
        } catch (Exception e) {
            log.error("提交短信异常 - systemId: {}", account.getSystemId(), e);
            return null;
        }
    }

    /**
     * 构造发送短信请求
     */
    private UniSendSmsRequest buildSendSmsRequest(SubmitSm submitSm, String message, SmppAccount account) {
        UniSendSmsRequest request = new UniSendSmsRequest();
        
        // 基础参数
        request.setPhoneNumber(submitSm.getDestAddress().getAddress());
        request.setSenderId(submitSm.getSourceAddress().getAddress());
        request.setContent(message);
        
        // TODO: 根据实际业务需求设置其他参数，这里先模拟数据
        request.setTemplateId(""); // 模板ID
        request.setParam(""); // 模板参数
        request.setOutId(generateOutId()); // 外部ID
        request.setDlrUrl(""); // 状态报告URL
        request.setInboundUrl(""); // 上行URL
        request.setExtend(account.getSystemId()); // 扩展字段，用于标识来源
        
        // 消息类型判断
        boolean isLongSms = message.length() > getMaxSingleSmsLength(account.getEncoding());
        // TODO: 设置消息帧类型和短信类型，需要根据实际枚举类型调整
        // request.setMsgFrameType(isLongSms ? MsgFrameTypeEnum.LONG : MsgFrameTypeEnum.SHORT);
        // request.setSmsType(SmsTypeEnum.NORMAL);
        
        log.debug("构造发送短信请求 - phone: {}, senderId: {}, isLongSms: {}, outId: {}", 
                request.getPhoneNumber(), request.getSenderId(), isLongSms, request.getOutId());
        
        return request;
    }

    /**
     * 获取单条短信最大长度
     */
    private int getMaxSingleSmsLength(Integer encoding) {
        if (encoding == null || encoding == 1) {
            return 160; // GSM 7-bit编码
        } else {
            return 70;  // 其他编码（如UCS2）
        }
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis();
    }

    /**
     * 生成外部ID
     */
    private String generateOutId() {
        return "SMPP_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 验证短信内容
     */
    public boolean validateMessage(String message, SmppAccount account) {
        if (message == null || message.isEmpty()) {
            log.warn("短信内容为空 - systemId: {}", account.getSystemId());
            return false;
        }
        
        // 检查最大长度限制
        int maxLength = getMaxMessageLength(account.getEncoding());
        if (message.length() > maxLength) {
            log.warn("短信内容超长 - systemId: {}, length: {}, maxLength: {}", 
                    account.getSystemId(), message.length(), maxLength);
            return false;
        }
        
        return true;
    }

    /**
     * 获取消息最大长度
     */
    private int getMaxMessageLength(Integer encoding) {
        // 长短信最大长度限制
        if (encoding == null || encoding == 1) {
            return 1530; // GSM 7-bit编码，约10条短信
        } else {
            return 670;  // 其他编码，约10条短信
        }
    }

    /**
     * 获取提交统计信息
     */
    public SubmitStatistics getSubmitStatistics() {
        // TODO: 实现统计逻辑
        SubmitStatistics stats = new SubmitStatistics();
        stats.setTotalSubmitted(0L);
        stats.setSuccessCount(0L);
        stats.setFailureCount(0L);
        return stats;
    }


}
