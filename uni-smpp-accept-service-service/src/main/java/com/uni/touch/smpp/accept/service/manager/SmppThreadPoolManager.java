package com.uni.touch.smpp.accept.service.manager;

import com.uni.touch.smpp.accept.common.config.SmppThreadPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * SMPP线程池管理器
 * 简化设计，基于ch-smpp内部线程池机制
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppThreadPoolManager {

    /**
     * IO工作线程池 - 传递给ch-smpp的DefaultSmppServer
     */
    private ExecutorService ioWorkerExecutor;

    /**
     * 监控线程池 - 传递给ch-smpp的DefaultSmppServer
     */
    private ScheduledExecutorService monitorExecutor;

    /**
     * 业务处理线程池 - 处理业务逻辑
     */
    private ExecutorService businessExecutor;

    @PostConstruct
    public void init() {
        log.info("初始化SMPP线程池管理器...");

        // 使用常量配置创建线程池
        createIoWorkerExecutor();
        createMonitorExecutor();
        createBusinessExecutor();

        log.info("SMPP线程池管理器初始化完成 - IO工作线程: {}, 监控线程: {}, 业务处理线程: {}",
                SmppThreadPoolConfig.IO_WORKER_THREADS,
                SmppThreadPoolConfig.MONITOR_THREADS,
                SmppThreadPoolConfig.BUSINESS_THREADS);
    }

    @PreDestroy
    public void destroy() {
        log.info("销毁SMPP线程池管理器...");
        
        shutdownExecutor(ioWorkerExecutor, "IO工作线程池");
        shutdownExecutor(monitorExecutor, "监控线程池");
        shutdownExecutor(businessExecutor, "业务处理线程池");
        
        log.info("SMPP线程池管理器销毁完成");
    }

    /**
     * 创建IO工作线程池
     */
    private void createIoWorkerExecutor() {
        this.ioWorkerExecutor = new ThreadPoolExecutor(
                SmppThreadPoolConfig.IO_WORKER_THREADS,
                SmppThreadPoolConfig.IO_WORKER_THREADS,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(SmppThreadPoolConfig.QUEUE_SIZE),
                new SmppThreadFactory("SMPP-IO-Worker"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 创建监控线程池
     */
    private void createMonitorExecutor() {
        this.monitorExecutor = new ScheduledThreadPoolExecutor(
                SmppThreadPoolConfig.MONITOR_THREADS,
                new SmppThreadFactory("SMPP-Monitor")
        );
    }

    /**
     * 创建业务处理线程池
     */
    private void createBusinessExecutor() {
        this.businessExecutor = new ThreadPoolExecutor(
                SmppThreadPoolConfig.BUSINESS_THREADS,
                SmppThreadPoolConfig.BUSINESS_THREADS,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(SmppThreadPoolConfig.QUEUE_SIZE),
                new SmppThreadFactory("SMPP-Business"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 获取IO工作线程池
     */
    public ExecutorService getIoWorkerExecutor() {
        return ioWorkerExecutor;
    }

    /**
     * 获取监控线程池
     */
    public ScheduledExecutorService getMonitorExecutor() {
        return monitorExecutor;
    }

    /**
     * 获取业务处理线程池
     */
    public ExecutorService getBusinessExecutor() {
        return businessExecutor;
    }

    /**
     * 关闭线程池
     */
    private void shutdownExecutor(ExecutorService executor, String name) {
        if (executor == null) {
            return;
        }

        try {
            log.info("关闭{}...", name);
            executor.shutdown();
            
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("{}未能在5秒内正常关闭，强制关闭", name);
                executor.shutdownNow();
                
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("{}无法关闭", name);
                }
            }
            
            log.info("{}关闭完成", name);
            
        } catch (InterruptedException e) {
            log.warn("关闭{}时被中断", name);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * SMPP线程工厂
     */
    private static class SmppThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private int counter = 0;

        public SmppThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-" + (++counter));
            thread.setDaemon(true);
            return thread;
        }
    }
}
