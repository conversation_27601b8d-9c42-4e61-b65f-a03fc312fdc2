package com.uni.touch.smpp.accept.service.utils;

import com.cloudhopper.smpp.type.SmppProcessingException;
import com.uni.touch.smpp.accept.service.exception.SmppErrorInfo;
import com.uni.touch.smpp.accept.service.exception.SmppExceptionUtils;

/**
 * SMPP专用断言工具类
 * 直接抛出SmppProcessingException，与ch-smpp框架完全兼容
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
public class SmppAssertUtils {

    /**
     * 断言条件为真，否则抛出SmppProcessingException
     *
     * 这是唯一的断言方法，保持与原有AssertUtils.isTrue的一致性
     *
     * @param condition 条件
     * @param errorInfo 错误信息定义
     * @param args 格式化参数
     * @throws SmppProcessingException 当条件为假时抛出，包含SMPP标准状态码
     */
    public static void isTrue(boolean condition, SmppErrorInfo errorInfo, Object... args) throws SmppProcessingException {
        if (!condition) {
            throw SmppExceptionUtils.createSmppException(errorInfo, args);
        }
    }
}
