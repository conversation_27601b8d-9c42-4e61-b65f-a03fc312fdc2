package com.uni.touch.smpp.accept.service.processor;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.pdu.DeliverSm;
import com.cloudhopper.smpp.tlv.Tlv;
import com.cloudhopper.smpp.type.Address;
import com.cloudhopper.smpp.util.DeliveryReceipt;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

/**
 * SMPP状态报告处理器
 * 基于ch-smpp框架构造标准的DeliverSm状态报告
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppDeliveryProcessor {

    /**
     * 构造状态报告DeliverSm
     */
    public DeliverSm buildDeliveryReceipt(String messageId, String phoneNumber, 
                                        byte deliveryState, String systemId, 
                                        String originalText) {
        try {
            log.info("构造状态报告 - messageId: {}, phone: {}, state: {}, systemId: {}", 
                    messageId, phoneNumber, deliveryState, systemId);

            DeliverSm deliverSm = new DeliverSm();
            
            // 1. 设置地址信息
            deliverSm.setSourceAddress(new Address((byte)1, (byte)1, phoneNumber));
            deliverSm.setDestAddress(new Address((byte)0, (byte)0, systemId));
            
            // 2. 设置ESM Class为状态报告
            deliverSm.setEsmClass(SmppConstants.ESM_CLASS_MT_SMSC_DELIVERY_RECEIPT);
            
            // 3. 设置数据编码
            deliverSm.setDataCoding(SmppConstants.DATA_CODING_DEFAULT);
            
            // 4. 构造DeliveryReceipt标准格式内容
            DeliveryReceipt receipt = createDeliveryReceipt(messageId, deliveryState, originalText);
            
            // 5. 生成标准格式的状态报告文本
            String receiptText = receipt.toShortMessage();
            deliverSm.setShortMessage(receiptText.getBytes("ISO-8859-1"));
            
            // 6. 设置可选参数
            deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_RECEIPTED_MSG_ID, messageId.getBytes()));
            deliverSm.addOptionalParameter(new Tlv(SmppConstants.TAG_MSG_STATE, new byte[]{deliveryState}));
            
            log.info("状态报告构造完成 - messageId: {}, receiptText: {}", messageId, receiptText);
            
            return deliverSm;
            
        } catch (Exception e) {
            log.error("构造状态报告异常 - messageId: {}, phone: {}", messageId, phoneNumber, e);
            return null;
        }
    }

    /**
     * 构造DeliveryReceipt对象
     */
    private DeliveryReceipt createDeliveryReceipt(String messageId, byte deliveryState, String originalText) {
        DeliveryReceipt receipt = new DeliveryReceipt();
        
        // 设置消息ID
        receipt.setMessageId(messageId);
        
        // 设置投递状态
        receipt.setState(deliveryState);
        
        // 设置提交和投递计数
        receipt.setSubmitCount(1);
        receipt.setDeliveredCount(deliveryState == SmppConstants.STATE_DELIVERED ? 1 : 0);
        
        // 设置时间信息
        DateTime now = new DateTime();
        receipt.setSubmitDate(now.minusMinutes(1)); // 假设1分钟前提交
        receipt.setDoneDate(now);
        
        // 设置错误码
        receipt.setErrorCode(getErrorCode(deliveryState));
        
        // 设置原始短信内容（前20个字符）
        if (originalText != null && originalText.length() > 20) {
            receipt.setText(originalText.substring(0, 20));
        } else {
            receipt.setText(originalText != null ? originalText : "");
        }
        
        return receipt;
    }

    /**
     * 根据投递状态获取错误码
     */
    private int getErrorCode(byte deliveryState) {
        switch (deliveryState) {
            case SmppConstants.STATE_DELIVERED:
                return 0; // 成功投递
            case SmppConstants.STATE_EXPIRED:
                return 1; // 消息过期
            case SmppConstants.STATE_DELETED:
                return 2; // 消息被删除
            case SmppConstants.STATE_UNDELIVERABLE:
                return 3; // 无法投递
            case SmppConstants.STATE_ACCEPTED:
                return 0; // 已接受
            case SmppConstants.STATE_UNKNOWN:
                return 4; // 未知状态
            case SmppConstants.STATE_REJECTED:
                return 5; // 被拒绝
            default:
                return 99; // 其他错误
        }
    }

    /**
     * 获取投递状态描述
     */
    public String getDeliveryStateDescription(byte deliveryState) {
        switch (deliveryState) {
            case SmppConstants.STATE_ENROUTE:
                return "ENROUTE";
            case SmppConstants.STATE_DELIVERED:
                return "DELIVRD";
            case SmppConstants.STATE_EXPIRED:
                return "EXPIRED";
            case SmppConstants.STATE_DELETED:
                return "DELETED";
            case SmppConstants.STATE_UNDELIVERABLE:
                return "UNDELIV";
            case SmppConstants.STATE_ACCEPTED:
                return "ACCEPTD";
            case SmppConstants.STATE_UNKNOWN:
                return "UNKNOWN";
            case SmppConstants.STATE_REJECTED:
                return "REJECTD";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 解析状态字符串为状态码
     */
    public byte parseDeliveryState(String stateString) {
        if (stateString == null) {
            return SmppConstants.STATE_UNKNOWN;
        }
        
        switch (stateString.toUpperCase()) {
            case "ENROUTE":
                return SmppConstants.STATE_ENROUTE;
            case "DELIVRD":
            case "DELIVERED":
                return SmppConstants.STATE_DELIVERED;
            case "EXPIRED":
                return SmppConstants.STATE_EXPIRED;
            case "DELETED":
                return SmppConstants.STATE_DELETED;
            case "UNDELIV":
            case "UNDELIVERABLE":
                return SmppConstants.STATE_UNDELIVERABLE;
            case "ACCEPTD":
            case "ACCEPTED":
                return SmppConstants.STATE_ACCEPTED;
            case "REJECTD":
            case "REJECTED":
                return SmppConstants.STATE_REJECTED;
            default:
                return SmppConstants.STATE_UNKNOWN;
        }
    }

    /**
     * 验证状态报告格式
     */
    public boolean validateDeliveryReceipt(DeliverSm deliverSm) {
        if (deliverSm == null) {
            return false;
        }
        
        // 检查ESM Class
        if (deliverSm.getEsmClass() != SmppConstants.ESM_CLASS_MT_SMSC_DELIVERY_RECEIPT) {
            return false;
        }
        
        // 检查是否有消息内容
        if (deliverSm.getShortMessage() == null || deliverSm.getShortMessage().length == 0) {
            return false;
        }
        
        // 检查可选参数
        if (!deliverSm.hasOptionalParameter(SmppConstants.TAG_RECEIPTED_MSG_ID)) {
            return false;
        }
        
        return true;
    }
}
