package com.uni.touch.smpp.accept.service.entity;

import com.cloudhopper.smpp.pdu.SubmitSm;
import lombok.Data;

/**
 * 重组后的完整消息实体
 * 用于分段消息重组完成后的结果封装
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class ReassembledMessage {
    
    /**
     * 原始的第一个分段SubmitSm（用于构造响应）
     */
    private SubmitSm originalSubmitSm;
    
    /**
     * 重组后的完整消息内容
     */
    private String completeMessage;
    
    /**
     * 消息参考号
     */
    private int refNum;
    
    /**
     * 总分段数
     */
    private int totalSegments;
    
    /**
     * 分段类型
     */
    private SegmentType type;

    /**
     * 构造方法
     */
    public ReassembledMessage(SubmitSm originalSubmitSm, String completeMessage, 
                            int refNum, int totalSegments, SegmentType type) {
        this.originalSubmitSm = originalSubmitSm;
        this.completeMessage = completeMessage;
        this.refNum = refNum;
        this.totalSegments = totalSegments;
        this.type = type;
    }
}
