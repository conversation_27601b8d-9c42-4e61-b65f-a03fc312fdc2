package com.uni.touch.smpp.accept.service.entity;

import lombok.Data;

/**
 * SMPP UDH分段信息实体
 * 用于UDH方式的长短信分段处理
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class SmppUdhInfo {
    
    /**
     * 消息参考号
     */
    private int refNum;
    
    /**
     * 总分段数
     */
    private int totalParts;
    
    /**
     * 当前分段号
     */
    private int partNum;
    
    /**
     * 用户数据（去除UDH后的内容）
     */
    private byte[] userData;
    
    /**
     * 验证UDH信息是否有效
     */
    public boolean isValid() {
        return refNum > 0 && totalParts > 0 && partNum > 0 && 
               partNum <= totalParts && userData != null;
    }
    
    /**
     * 生成缓存键
     */
    public String generateCacheKey(String systemId) {
        return "UDH_" + systemId + "_" + refNum;
    }
}
