package com.uni.touch.smpp.accept.service.entity;

import lombok.Data;

/**
 * SMPP SAR分段信息实体
 * 用于SAR TLV方式的长短信分段处理
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class SmppSarInfo {
    
    /**
     * 消息参考号
     */
    private int refNum;
    
    /**
     * 总分段数
     */
    private int totalSegments;
    
    /**
     * 当前分段序号
     */
    private int segmentSeq;
    
    /**
     * 验证SAR信息是否有效
     */
    public boolean isValid() {
        return refNum > 0 && totalSegments > 0 && segmentSeq > 0 && segmentSeq <= totalSegments;
    }
    
    /**
     * 生成缓存键
     */
    public String generateCacheKey(String systemId) {
        return "SAR_" + systemId + "_" + refNum;
    }
}
