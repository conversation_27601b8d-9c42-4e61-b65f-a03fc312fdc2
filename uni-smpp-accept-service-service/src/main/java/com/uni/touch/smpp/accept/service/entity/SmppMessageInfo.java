package com.uni.touch.smpp.accept.service.entity;

import lombok.Data;

/**
 * SMPP消息信息实体
 * 封装解析后的消息内容和相关信息
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class SmppMessageInfo {
    
    /**
     * 消息类型
     */
    private MessageType messageType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 数据编码
     */
    private byte dataCoding;
    
    /**
     * 是否需要分段处理
     */
    private boolean needsSegmentation;
    
    /**
     * SAR分段信息（如果是SAR类型）
     */
    private SmppSarInfo sarInfo;
    
    /**
     * UDH分段信息（如果是UDH类型）
     */
    private SmppUdhInfo udhInfo;
    

    
    /**
     * 创建普通消息信息
     */
    public static SmppMessageInfo createRegular(String content, byte dataCoding) {
        SmppMessageInfo info = new SmppMessageInfo();
        info.setMessageType(MessageType.REGULAR);
        info.setContent(content);
        info.setDataCoding(dataCoding);
        info.setNeedsSegmentation(false);
        return info;
    }
    
    /**
     * 创建SAR分段消息信息
     */
    public static SmppMessageInfo createSarSegmented(SmppSarInfo sarInfo, byte dataCoding) {
        SmppMessageInfo info = new SmppMessageInfo();
        info.setMessageType(MessageType.SAR_SEGMENTED);
        info.setSarInfo(sarInfo);
        info.setDataCoding(dataCoding);
        info.setNeedsSegmentation(true);
        return info;
    }
    
    /**
     * 创建UDH分段消息信息
     */
    public static SmppMessageInfo createUdhSegmented(SmppUdhInfo udhInfo, byte dataCoding) {
        SmppMessageInfo info = new SmppMessageInfo();
        info.setMessageType(MessageType.UDH_SEGMENTED);
        info.setUdhInfo(udhInfo);
        info.setDataCoding(dataCoding);
        info.setNeedsSegmentation(true);
        return info;
    }
    
    /**
     * 创建message_payload消息信息
     */
    public static SmppMessageInfo createMessagePayload(String content, byte dataCoding) {
        SmppMessageInfo info = new SmppMessageInfo();
        info.setMessageType(MessageType.MESSAGE_PAYLOAD);
        info.setContent(content);
        info.setDataCoding(dataCoding);
        info.setNeedsSegmentation(false);
        return info;
    }
}
