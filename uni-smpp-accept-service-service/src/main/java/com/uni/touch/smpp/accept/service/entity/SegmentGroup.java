package com.uni.touch.smpp.accept.service.entity;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分段消息组实体
 * 用于管理和重组分段消息
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
@Slf4j
public class SegmentGroup {
    
    /**
     * 缓存键
     */
    private String cacheKey;
    
    /**
     * 系统ID
     */
    private String systemId;
    
    /**
     * 消息参考号
     */
    private int refNum;
    
    /**
     * 总分段数
     */
    private int totalSegments;
    
    /**
     * 分段类型
     */
    private SegmentType type;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 分段映射（分段号 -> SubmitSm）
     */
    private Map<Integer, SubmitSm> segments;

    /**
     * 构造方法
     */
    public SegmentGroup(String cacheKey, String systemId, int refNum, 
                      int totalSegments, SegmentType type) {
        this.cacheKey = cacheKey;
        this.systemId = systemId;
        this.refNum = refNum;
        this.totalSegments = totalSegments;
        this.type = type;
        this.createdTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        this.segments = new ConcurrentHashMap<>();
    }

    /**
     * 添加分段
     */
    public boolean addSegment(int segmentNum, SubmitSm submitSm) {
        if (segmentNum < 1 || segmentNum > totalSegments) {
            return false;
        }
        
        segments.put(segmentNum, submitSm);
        lastUpdateTime = LocalDateTime.now();
        return true;
    }

    /**
     * 检查是否完整
     */
    public boolean isComplete() {
        return segments.size() == totalSegments;
    }

    /**
     * 重组消息
     */
    public ReassembledMessage reassemble(SmppEncodingConverter encodingConverter) {
        if (!isComplete()) {
            return null;
        }

        StringBuilder completeMessage = new StringBuilder();
        SubmitSm firstSegment = null;

        // 按序号重组消息
        for (int i = 1; i <= totalSegments; i++) {
            SubmitSm segment = segments.get(i);
            if (segment == null) {
                throw new IllegalStateException("缺失分段: " + i);
            }
            
            if (firstSegment == null) {
                firstSegment = segment;
            }
            
            // 解码并拼接消息内容
            byte[] messageBytes = segment.getShortMessage();
            if (messageBytes != null && messageBytes.length > 0) {
                // 使用第一个分段的编码信息
                String segmentText = encodingConverter.decodeMessage(
                    messageBytes, getEncodingFromDataCoding(firstSegment.getDataCoding()));
                completeMessage.append(segmentText);
            }
        }

        return new ReassembledMessage(firstSegment, completeMessage.toString(), 
                                    refNum, totalSegments, type);
    }

    /**
     * 根据dataCoding获取编码类型
     */
    private Integer getEncodingFromDataCoding(byte dataCoding) {
        // 根据dataCoding推断smppEncoding（基于CharsetUtil常量）
        switch (dataCoding) {
            case SmppConstants.DATA_CODING_DEFAULT:
                return 1; // GSM 7-bit
            case SmppConstants.DATA_CODING_IA5:
                return 2; // ASCII/IA5
            case SmppConstants.DATA_CODING_LATIN1:
                return 3; // Latin-1/ISO-8859-1
            case SmppConstants.DATA_CODING_UCS2:
                return 4; // UCS2
            case SmppConstants.DATA_CODING_8BIT:
                return 3; // 8-bit binary -> Latin-1
            default:
                log.warn("未知的dataCoding: 0x{}, 使用默认GSM编码", Integer.toHexString(dataCoding));
                return 1; // 默认GSM 7-bit
        }
    }
}
