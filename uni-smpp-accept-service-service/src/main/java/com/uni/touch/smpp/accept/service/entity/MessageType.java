package com.uni.touch.smpp.accept.service.entity;

/**
 * SMPP消息类型枚举
 * 定义不同类型的SMPP消息
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
public enum MessageType {
    
    /**
     * 普通消息
     * 使用short_message字段的标准短信
     */
    REGULAR,
    
    /**
     * SAR TLV分段消息
     * 使用SAR TLV参数进行分段的长短信
     */
    SAR_SEGMENTED,
    
    /**
     * UDH分段消息
     * 使用User Data Header进行分段的长短信
     */
    UDH_SEGMENTED,
    
    /**
     * Message Payload消息
     * 使用message_payload TLV参数的长消息
     */
    MESSAGE_PAYLOAD
}
