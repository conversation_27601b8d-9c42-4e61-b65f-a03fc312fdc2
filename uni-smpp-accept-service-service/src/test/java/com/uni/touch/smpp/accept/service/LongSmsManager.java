// package com.uni.touch.smpp.accept.service;
//
// import com.cloudhopper.smpp.pdu.SubmitSm;
// import com.cloudhopper.smpp.tlv.Tlv;
// import com.cloudhopper.smpp.tlv.TlvConvertException;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.stereotype.Component;
//
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.concurrent.ConcurrentHashMap;
// import java.util.concurrent.Executors;
// import java.util.concurrent.ScheduledExecutorService;
// import java.util.concurrent.TimeUnit;
//
// /**
//  * 长短信管理器
//  * 负责处理分段消息的缓存、重组和超时清理
//  */
// @Component
// public class LongSmsManager {
//
//     private static final Logger logger = LoggerFactory.getLogger(LongSmsManager.class);
//
//     // SAR TLV标签
//     private static final short SAR_MSG_REF_NUM = 0x020C;
//     private static final short SAR_TOTAL_SEGMENTS = 0x020E;
//     private static final short SAR_SEGMENT_SEQNUM = 0x020F;
//
//     // 分段消息超时时间（分钟）
//     private static final int SEGMENT_TIMEOUT_MINUTES = 5;
//
//     // 分段消息缓存：key = systemId:messageRefNum, value = SegmentedMessage
//     private final Map<String, SegmentedMessage> segmentCache = new ConcurrentHashMap<>();
//
//     // 定时清理器
//     private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
//
//     public LongSmsManager() {
//         // 每分钟清理一次过期分段
//         cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredSegments, 1, 1, TimeUnit.MINUTES);
//     }
//
//     /**
//      * 检查是否为分段消息
//      */
//     public boolean isSegmentedMessage(SubmitSm submitSm) {
//         return submitSm.getOptionalParameters() != null &&
//                hasSarTlv(submitSm, SAR_MSG_REF_NUM) &&
//                hasSarTlv(submitSm, SAR_TOTAL_SEGMENTS) &&
//                hasSarTlv(submitSm, SAR_SEGMENT_SEQNUM);
//     }
//
//     /**
//      * 处理分段消息
//      * @return 如果重组完成返回完整消息，否则返回null
//      */
//     public ReassembledMessage processSegment(SubmitSm submitSm, String systemId) {
//         try {
//             // 提取SAR参数
//             int messageRefNum = getSarTlvValue(submitSm, SAR_MSG_REF_NUM);
//             int totalSegments = getSarTlvValue(submitSm, SAR_TOTAL_SEGMENTS);
//             int segmentSeqNum = getSarTlvValue(submitSm, SAR_SEGMENT_SEQNUM);
//
//             logger.debug("处理分段消息: systemId={}, refNum={}, segment={}/{}",
//                         systemId, messageRefNum, segmentSeqNum, totalSegments);
//
//             String cacheKey = systemId + ":" + messageRefNum;
//
//             // 获取或创建分段消息对象
//             SegmentedMessage segmentedMsg = segmentCache.computeIfAbsent(cacheKey,
//                 k -> new SegmentedMessage(messageRefNum, totalSegments, systemId));
//
//             // 添加分段
//             segmentedMsg.addSegment(segmentSeqNum, submitSm);
//
//             // 检查是否收齐所有分段
//             if (segmentedMsg.isComplete()) {
//                 logger.info("长短信重组完成: systemId={}, refNum={}, totalSegments={}",
//                            systemId, messageRefNum, totalSegments);
//
//                 // 重组消息并从缓存中移除
//                 ReassembledMessage reassembled = segmentedMsg.reassemble();
//                 segmentCache.remove(cacheKey);
//                 return reassembled;
//             }
//
//             logger.debug("等待更多分段: systemId={}, refNum={}, received={}/{}",
//                         systemId, messageRefNum, segmentedMsg.getReceivedCount(), totalSegments);
//             return null;
//
//         } catch (Exception e) {
//             logger.error("处理分段消息失败: systemId={}", systemId, e);
//             return null;
//         }
//     }
//
//     /**
//      * 检查是否存在指定的SAR TLV
//      */
//     private boolean hasSarTlv(SubmitSm submitSm, short tag) {
//         if (submitSm.getOptionalParameters() == null) {
//             return false;
//         }
//         return submitSm.getOptionalParameter(tag) != null;
//     }
//
//     /**
//      * 获取SAR TLV的值
//      */
//     private int getSarTlvValue(SubmitSm submitSm, short tag) throws TlvConvertException {
//         Tlv tlv = submitSm.getOptionalParameter(tag);
//         if (tlv == null) {
//             throw new IllegalArgumentException("Missing SAR TLV: " + tag);
//         }
//
//         if (tlv.getLength() == 1) {
//             return tlv.getValue()[0] & 0xFF;
//         } else if (tlv.getLength() == 2) {
//             return ((tlv.getValue()[0] & 0xFF) << 8) | (tlv.getValue()[1] & 0xFF);
//         } else {
//             throw new IllegalArgumentException("Invalid SAR TLV length: " + tlv.getLength());
//         }
//     }
//
//     /**
//      * 清理过期的分段消息
//      */
//     private void cleanupExpiredSegments() {
//         long currentTime = System.currentTimeMillis();
//         List<String> expiredKeys = new ArrayList<>();
//
//         for (Map.Entry<String, SegmentedMessage> entry : segmentCache.entrySet()) {
//             if (currentTime - entry.getValue().getCreatedTime() > SEGMENT_TIMEOUT_MINUTES * 60 * 1000L) {
//                 expiredKeys.add(entry.getKey());
//             }
//         }
//
//         for (String key : expiredKeys) {
//             SegmentedMessage removed = segmentCache.remove(key);
//             if (removed != null) {
//                 logger.warn("清理过期分段消息: systemId={}, refNum={}, received={}/{}",
//                            removed.getSystemId(), removed.getMessageRefNum(),
//                            removed.getReceivedCount(), removed.getTotalSegments());
//             }
//         }
//
//         if (!expiredKeys.isEmpty()) {
//             logger.debug("清理了{}个过期分段消息", expiredKeys.size());
//         }
//     }
//
//     /**
//      * 分段消息类
//      */
//     private static class SegmentedMessage {
//         private final int messageRefNum;
//         private final int totalSegments;
//         private final String systemId;
//         private final long createdTime;
//         private final Map<Integer, SubmitSm> segments = new HashMap<>();
//
//         public SegmentedMessage(int messageRefNum, int totalSegments, String systemId) {
//             this.messageRefNum = messageRefNum;
//             this.totalSegments = totalSegments;
//             this.systemId = systemId;
//             this.createdTime = System.currentTimeMillis();
//         }
//
//         public void addSegment(int segmentSeqNum, SubmitSm submitSm) {
//             segments.put(segmentSeqNum, submitSm);
//         }
//
//         public boolean isComplete() {
//             return segments.size() == totalSegments;
//         }
//
//         public int getReceivedCount() {
//             return segments.size();
//         }
//
//         public ReassembledMessage reassemble() {
//             StringBuilder fullMessage = new StringBuilder();
//             SubmitSm firstSegment = null;
//
//             // 按序号重组消息
//             for (int i = 1; i <= totalSegments; i++) {
//                 SubmitSm segment = segments.get(i);
//                 if (segment == null) {
//                     throw new IllegalStateException("Missing segment: " + i);
//                 }
//
//                 if (firstSegment == null) {
//                     firstSegment = segment;
//                 }
//
//                 // 拼接消息内容
//                 byte[] messageBytes = segment.getShortMessage();
//                 if (messageBytes != null && messageBytes.length > 0) {
//                     fullMessage.append(new String(messageBytes));
//                 }
//             }
//
//             return new ReassembledMessage(firstSegment, fullMessage.toString(), messageRefNum, totalSegments);
//         }
//
//         // Getters
//         public int getMessageRefNum() { return messageRefNum; }
//         public int getTotalSegments() { return totalSegments; }
//         public String getSystemId() { return systemId; }
//         public long getCreatedTime() { return createdTime; }
//     }
//
//     /**
//      * 重组后的完整消息
//      */
//     public static class ReassembledMessage {
//         private final SubmitSm originalSegment;
//         private final String fullMessage;
//         private final int messageRefNum;
//         private final int totalSegments;
//
//         public ReassembledMessage(SubmitSm originalSegment, String fullMessage, int messageRefNum, int totalSegments) {
//             this.originalSegment = originalSegment;
//             this.fullMessage = fullMessage;
//             this.messageRefNum = messageRefNum;
//             this.totalSegments = totalSegments;
//         }
//
//         public SubmitSm getOriginalSegment() { return originalSegment; }
//         public String getFullMessage() { return fullMessage; }
//         public int getMessageRefNum() { return messageRefNum; }
//         public int getTotalSegments() { return totalSegments; }
//     }
//
//     /**
//      * 获取统计信息
//      */
//     public Map<String, Object> getStatistics() {
//         Map<String, Object> stats = new HashMap<>();
//         stats.put("cached_segments", segmentCache.size());
//         stats.put("timeout_minutes", SEGMENT_TIMEOUT_MINUTES);
//         return stats;
//     }
//
//     /**
//      * 关闭清理器
//      */
//     public void shutdown() {
//         cleanupExecutor.shutdown();
//     }
// }