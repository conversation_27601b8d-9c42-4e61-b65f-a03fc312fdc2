// package com.uni.touch.smpp.accept.service;
//
// import com.cloudhopper.smpp.PduAsyncResponse;
// import com.cloudhopper.smpp.SmppConstants;
// import com.cloudhopper.smpp.pdu.*;
// import com.cloudhopper.smpp.type.RecoverablePduException;
// import com.cloudhopper.smpp.type.UnrecoverablePduException;
// import com.uni.touch.sms.common.entity.SmppUser;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
//
// /**
//  * SMPP会话处理器，处理具体的SMPP消息
//  */
// public class SmppSessionHandler implements com.cloudhopper.smpp.SmppSessionHandler {
//
//     private static final Logger logger = LoggerFactory.getLogger(SmppSessionHandler.class);
//
//     private final SmppUser user;
//     private final SmppSessionManager sessionManager;
//     private final LongSmsManager longSmsManager;
//
//     public SmppSessionHandler(SmppUser user, SmppSessionManager sessionManager, LongSmsManager longSmsManager) {
//         this.user = user;
//         this.sessionManager = sessionManager;
//         this.longSmsManager = longSmsManager;
//     }
//
//     @Override
//     public String lookupTlvTagName(short tag) {
//         // 提供TLV标签名查找的默认实现
//         return "Unknown_TLV_" + tag;
//     }
//
//     @Override
//     public String lookupResultMessage(int commandStatus) {
//         // 提供结果消息查找的默认实现
//         return "Status_" + commandStatus;
//     }
//
//     @Override
//     public PduResponse firePduRequestReceived(PduRequest pduRequest) {
//         logger.debug("PDU request received: {}, systemId: {}", pduRequest.getName(), user.getSystemId());
//
//         try {
//             switch (pduRequest.getCommandId()) {
//                 case SmppConstants.CMD_ID_SUBMIT_SM:
//                     return handleSubmitSm((SubmitSm) pduRequest);
//                 case SmppConstants.CMD_ID_QUERY_SM:
//                     return handleQuerySm((QuerySm) pduRequest);
//                 case SmppConstants.CMD_ID_CANCEL_SM:
//                     return handleCancelSm((CancelSm) pduRequest);
//                 case SmppConstants.CMD_ID_ENQUIRE_LINK:
//                     return handleEnquireLink((EnquireLink) pduRequest);
//                 case SmppConstants.CMD_ID_UNBIND:
//                     return handleUnbind((Unbind) pduRequest);
//                 default:
//                     logger.warn("Unsupported PDU command: {}, systemId: {}", pduRequest.getCommandId(), user.getSystemId());
//                     return pduRequest.createResponse();
//             }
//         } catch (Exception e) {
//             logger.error("Error processing PDU request: {}, systemId: {}", pduRequest.getName(), user.getSystemId(), e);
//             return pduRequest.createResponse();
//         }
//     }
//
//     /**
//      * 处理短消息提交
//      */
//     private PduResponse handleSubmitSm(SubmitSm submitSm) {
//         // 记录原始消息信息（包含SAR参数的详细日志）
//         logSubmitSmDetails(submitSm);
//
//         // 检查是否为分段消息
//         if (longSmsManager.isSegmentedMessage(submitSm)) {
//             return handleSegmentedMessage(submitSm);
//         } else {
//             return handleRegularMessage(submitSm);
//         }
//     }
//
//     /**
//      * 记录submit_sm的详细信息
//      */
//     private void logSubmitSmDetails(SubmitSm submitSm) {
//         String message = new String(submitSm.getShortMessage() != null ? submitSm.getShortMessage() : new byte[0]);
//
//         logger.info("收到短消息提交: from={}, to={}, message={}, dcs=0x{:02X}, length={}, systemId={}",
//                    submitSm.getSourceAddress(), submitSm.getDestAddress(),
//                    message, submitSm.getDataCoding(), message.length(), user.getSystemId());
//
//         // 记录SAR参数（如果存在）
//         if (submitSm.getOptionalParameters() != null && !submitSm.getOptionalParameters().isEmpty()) {
//             logger.debug("TLV参数: {}", submitSm.getOptionalParameters());
//         }
//     }
//
//     /**
//      * 处理分段消息
//      */
//     private PduResponse handleSegmentedMessage(SubmitSm submitSm) {
//         logger.info("检测到分段消息，开始处理长短信重组: systemId={}", user.getSystemId());
//
//         // 处理分段
//         LongSmsManager.ReassembledMessage reassembled = longSmsManager.processSegment(submitSm, user.getSystemId());
//
//         if (reassembled != null) {
//             // 重组完成，处理完整消息
//             logger.info("长短信重组成功: systemId={}, refNum={}, totalSegments={}, fullMessage={}",
//                        user.getSystemId(), reassembled.getMessageRefNum(),
//                        reassembled.getTotalSegments(), reassembled.getFullMessage());
//
//             return processCompleteMessage(reassembled.getOriginalSegment(), reassembled.getFullMessage());
//         } else {
//             // 分段缓存中，返回成功响应
//             logger.debug("分段已缓存，等待更多分段: systemId={}", user.getSystemId());
//
//             // 检查限流（分段也要检查）
//             if (!sessionManager.checkRateLimit(user.getSystemId(), "submit")) {
//                 logger.warn("Rate limit exceeded for segmented submit_sm, systemId: {}", user.getSystemId());
//                 SubmitSmResp response = (SubmitSmResp) submitSm.createResponse();
//                 response.setCommandStatus(SmppConstants.STATUS_THROTTLED);
//                 return response;
//             }
//
//             // 生成消息ID并返回成功
//             String messageId = sessionManager.generateMessageId();
//             SubmitSmResp response = (SubmitSmResp) submitSm.createResponse();
//             response.setMessageId(messageId);
//             response.setCommandStatus(SmppConstants.STATUS_OK);
//             return response;
//         }
//     }
//
//     /**
//      * 处理普通消息
//      */
//     private PduResponse handleRegularMessage(SubmitSm submitSm) {
//         String message = new String(submitSm.getShortMessage() != null ? submitSm.getShortMessage() : new byte[0]);
//         return processCompleteMessage(submitSm, message);
//     }
//
//     /**
//      * 处理完整消息（普通消息或重组后的长短信）
//      */
//     private PduResponse processCompleteMessage(SubmitSm submitSm, String fullMessage) {
//         logger.info("处理完整消息: from={}, to={}, fullMessage=[{}], length={}, systemId={}",
//                    submitSm.getSourceAddress(), submitSm.getDestAddress(),
//                    fullMessage, fullMessage.length(), user.getSystemId());
//
//         // 检查限流
//         if (!sessionManager.checkRateLimit(user.getSystemId(), "submit")) {
//             logger.warn("Rate limit exceeded for submit_sm, systemId: {}", user.getSystemId());
//             SubmitSmResp response = (SubmitSmResp) submitSm.createResponse();
//             response.setCommandStatus(SmppConstants.STATUS_THROTTLED);
//             return response;
//         }
//
//         // 生成消息ID
//         String messageId = sessionManager.generateMessageId();
//
//         // 创建响应
//         SubmitSmResp response = (SubmitSmResp) submitSm.createResponse();
//         response.setMessageId(messageId);
//         response.setCommandStatus(SmppConstants.STATUS_OK);
//
//         logger.info("消息处理成功: messageId={}, systemId={}", messageId, user.getSystemId());
//         return response;
//     }
//
//     /**
//      * 处理消息查询
//      */
//     private PduResponse handleQuerySm(QuerySm querySm) {
//         logger.info("处理消息查询: messageId={}, systemId={}", querySm.getMessageId(), user.getSystemId());
//
//         QuerySmResp response = (QuerySmResp) querySm.createResponse();
//         response.setMessageId(querySm.getMessageId());
//         response.setFinalDate(null);
//         response.setMessageState(SmppConstants.STATE_DELIVERED);
//         response.setErrorCode((byte) 0);
//         response.setCommandStatus(SmppConstants.STATUS_OK);
//
//         return response;
//     }
//
//     /**
//      * 处理消息取消
//      */
//     private PduResponse handleCancelSm(CancelSm cancelSm) {
//         logger.info("处理消息取消: messageId={}, systemId={}", cancelSm.getMessageId(), user.getSystemId());
//
//         CancelSmResp response = (CancelSmResp) cancelSm.createResponse();
//         response.setCommandStatus(SmppConstants.STATUS_OK);
//         return response;
//     }
//
//     /**
//      * 处理链路检测
//      */
//     private PduResponse handleEnquireLink(EnquireLink enquireLink) {
//         logger.debug("处理链路检测: systemId={}", user.getSystemId());
//
//         EnquireLinkResp response = (EnquireLinkResp) enquireLink.createResponse();
//         response.setCommandStatus(SmppConstants.STATUS_OK);
//         return response;
//     }
//
//     /**
//      * 处理断开连接
//      */
//     private PduResponse handleUnbind(Unbind unbind) {
//         logger.info("处理断开连接请求: systemId={}", user.getSystemId());
//
//         return unbind.createResponse();
//     }
//
//     @Override
//     public void fireExpectedPduResponseReceived(PduAsyncResponse pduAsyncResponse) {
//         logger.debug("Expected PDU response received: {}, systemId: {}",
//                     pduAsyncResponse.getResponse().getName(), user.getSystemId());
//     }
//
//     @Override
//     public void fireUnexpectedPduResponseReceived(PduResponse pduResponse) {
//         logger.warn("Unexpected PDU response received: {}, systemId: {}",
//                    pduResponse.getName(), user.getSystemId());
//     }
//
//     @Override
//     public void fireUnrecoverablePduException(UnrecoverablePduException e) {
//         logger.error("Unrecoverable PDU exception, systemId: {}", user.getSystemId(), e);
//     }
//
//     @Override
//     public void fireRecoverablePduException(RecoverablePduException e) {
//         logger.warn("Recoverable PDU exception, systemId: {}", user.getSystemId(), e);
//     }
//
//     @Override
//     public void fireUnknownThrowable(Throwable t) {
//         logger.error("Unknown throwable, systemId: {}", user.getSystemId(), t);
//     }
//
//     @Override
//     public void fireChannelUnexpectedlyClosed() {
//         logger.warn("Channel unexpectedly closed, systemId: {}", user.getSystemId());
//     }
//
//     @Override
//     public void firePduRequestExpired(PduRequest pduRequest) {
//         logger.warn("PDU request expired: {}, systemId: {}", pduRequest.getName(), user.getSystemId());
//     }
// }