<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>uni-smpp-accept-service</artifactId>
        <groupId>com.uni.touch</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>uni-smpp-accept-service-controller</artifactId>


    <dependencies>

        <dependency>
            <groupId>com.uni.touch</groupId>
            <artifactId>uni-smpp-accept-service-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.uni.touch</groupId>-->
<!--            <artifactId>uni-smpp-accept-service-api</artifactId>-->
<!--        </dependency>-->

    </dependencies>

</project>