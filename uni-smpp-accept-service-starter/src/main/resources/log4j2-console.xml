<?xml version="1.0" encoding="UTF-8"?>
<Configuration name="baseConf" status="WARN" monitorInterval="30">
    
    <Properties>
        <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %C{1}.%M(%L): trace_id=%X{trace_id} %msg%n"/>
        <property name="LOG_FILE_PATH" value="logs"/>
    </Properties>
    
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
    </Appenders>
    
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>
