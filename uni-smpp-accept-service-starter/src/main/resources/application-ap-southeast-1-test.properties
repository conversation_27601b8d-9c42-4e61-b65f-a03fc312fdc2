project.env=test

dubbo.registry.address=nacos://127.0.0.1:8848

logging.config=classpath:log4j2-file.xml

# 数据源配置
spring.datasource.url=********************************************************************************************************************************
spring.datasource.username=user
spring.datasource.password=unitouch@2024

# redis
spring.unitouch.cache.key-prefix=prod
spring.redis.host=r-t4nrgpy4kanq7ypw4k.redis.singapore.rds.aliyuncs.com
spring.redis.port=6379
#spring.redis.password=unitouch@2024
spring.redis.database=0

# rocketMq
#spring.unitouch.mq.groupId=GID_UNI_SMS_SERVICE_HUAN_QIU
#spring.unitouch.mq.nameServAddr=ep-t4nic96798670673eee3.epsrv-t4n5z2imzihz6h3ubxci.ap-southeast-1.privatelink.aliyuncs.com:8080
#spring.unitouch.mq.namespaceV2=rmq-cn-5v2461j6n12