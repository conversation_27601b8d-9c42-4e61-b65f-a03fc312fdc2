<?xml version="1.0" encoding="UTF-8"?>
<Configuration name="baseConf" status="WARN" monitorInterval="30">

    <Properties>
        <property name="LOG_PATTERN"
                  value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %C{1}.%M(%L): trace_id=%X{trace_id} %msg%n"/>
        <property name="LOG_FILE_PATH" value="./logs"/>
    </Properties>

    <Appenders>
        <RollingRandomAccessFile name="INFO_LOG" fileName="${LOG_FILE_PATH}/info.log"
                                 filePattern="${LOG_FILE_PATH}/info.log.%d{yyyy-MM-dd}.%i">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${LOG_FILE_PATH}" maxDepth="10">
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <LevelRangeFilter minLevel="INFO" maxLevel="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="WARN_LOG" fileName="${LOG_FILE_PATH}/warn.log"
                                 filePattern="${LOG_FILE_PATH}/warn.log.%d{yyyy-MM-dd}.%i">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${LOG_FILE_PATH}" maxDepth="10">
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <LevelRangeFilter minLevel="WARN" maxLevel="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ERROR_LOG" fileName="${LOG_FILE_PATH}/error.log"
                                 filePattern="${LOG_FILE_PATH}/error.log.%d{yyyy-MM-dd}.%i">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${LOG_FILE_PATH}" maxDepth="10">
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <LevelRangeFilter minLevel="ERROR" maxLevel="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="CONTROLLER_LOG" fileName="${LOG_FILE_PATH}/controller.log"
                                 filePattern="${LOG_FILE_PATH}/controller.log.%d{yyyy-MM-dd}.%i">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${LOG_FILE_PATH}" maxDepth="10">
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <LevelRangeFilter minLevel="INFO" maxLevel="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <logger name="controllerLogger" level="INFO" additivity="false">
            <AppenderRef ref="CONTROLLER_LOG"/>
        </logger>

        <Root level="debug">
            <AppenderRef ref="INFO_LOG"/>
            <AppenderRef ref="WARN_LOG"/>
            <AppenderRef ref="ERROR_LOG"/>
        </Root>
    </Loggers>
</Configuration>
