package com.uni.touch.smpp.accept.common.config;

/**
 * SMPP普通端口配置常量
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
public final class SmppNormalPortConfig {

    private SmppNormalPortConfig() {
        // 防止实例化
    }

    /** 是否启用普通端口 */
    public static final boolean ENABLED = true;

    /** 普通端口号 */
    public static final int PORT = 2775;

    /** 绑定地址 */
    public static final String HOST = "0.0.0.0";

    /** 最大连接数 */
    public static final int MAX_CONNECTIONS = 1000;
}
