package com.uni.touch.smpp.accept.common.config;

/**
 * SMPP服务器基础配置常量
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
public final class SmppServerConfig {

    private SmppServerConfig() {
        // 防止实例化
    }

    /** 是否启用SMPP服务 */
    public static final boolean ENABLED = true;

    /** 服务器名称 */
    public static final String NAME = "UniSmppServer";

    /** 系统ID */
    public static final String SYSTEM_ID = "UNI_SMPP";

    /** 绑定超时时间（毫秒） */
    public static final long BIND_TIMEOUT = 5000L;

    /** 连接超时时间（毫秒） */
    public static final long CONNECT_TIMEOUT = 5000L;

    /** 是否启用非阻塞Socket */
    public static final boolean NON_BLOCKING_SOCKETS_ENABLED = true;

    /** 是否重用地址 */
    public static final boolean REUSE_ADDRESS = true;

    /** 是否启用JMX */
    public static final boolean JMX_ENABLED = false;

    /** JMX域名 */
    public static final String JMX_DOMAIN = "com.uni.touch.smpp";

    /** 是否自动协商接口版本 */
    public static final boolean AUTO_NEGOTIATE_INTERFACE_VERSION = true;

    /** 接口版本 - SMPP 3.4 */
    public static final byte INTERFACE_VERSION = 0x34;

    /** 默认窗口大小 */
    public static final int DEFAULT_WINDOW_SIZE = 10;

    /** 默认窗口等待超时时间（毫秒） */
    public static final long DEFAULT_WINDOW_WAIT_TIMEOUT = 30000L;

    /** 默认请求过期超时时间（毫秒） */
    public static final long DEFAULT_REQUEST_EXPIRY_TIMEOUT = 30000L;

    /** 默认窗口监控间隔（毫秒） */
    public static final long DEFAULT_WINDOW_MONITOR_INTERVAL = 10000L;

    /** 是否启用默认会话计数器 */
    public static final boolean DEFAULT_SESSION_COUNTERS_ENABLED = true;
}
