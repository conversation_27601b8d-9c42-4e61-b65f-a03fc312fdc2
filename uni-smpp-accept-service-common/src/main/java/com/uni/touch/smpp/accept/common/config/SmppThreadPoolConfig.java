package com.uni.touch.smpp.accept.common.config;

/**
 * SMPP线程池配置常量
 * 基于ch-smpp架构，针对1000个SMPP会话连接优化
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
public final class SmppThreadPoolConfig {

    private SmppThreadPoolConfig() {
        // 防止实例化
    }

    /** 
     * IO工作线程池大小
     * 基于ch-smpp NIO架构，每个Worker线程可处理50-100个并发会话
     * 1000个会话推荐16个线程
     */
    public static final int IO_WORKER_THREADS = 16;

    /** 
     * 监控线程池大小
     * 负责窗口监控、超时检测、会话健康检查
     * 每个监控线程可处理250-500个会话的监控任务
     */
    public static final int MONITOR_THREADS = 4;

    /** 
     * 业务处理线程池大小
     * 主要用于自定义业务逻辑处理，不直接参与SMPP协议处理
     */
    public static final int BUSINESS_THREADS = 12;

    /** 线程池队列大小 */
    public static final int QUEUE_SIZE = 1000;
}
