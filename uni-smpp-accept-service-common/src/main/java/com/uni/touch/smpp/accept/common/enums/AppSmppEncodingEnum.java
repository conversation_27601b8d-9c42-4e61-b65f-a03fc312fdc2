package com.uni.touch.smpp.accept.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * SMPP 默认编码方案枚举
 * 1-GSM-7, 2-ASCII, 3-Latin-1
 */
@Getter
@AllArgsConstructor
public enum AppSmppEncodingEnum {

    /**
     * GSM-7 编码 (默认)
     */
    GSM_7(1, "GSM-7"),

    /**
     * ASCII 编码
     */
    ASCII(2, "ASCII"),

    /**
     * Latin-1 (ISO-8859-1) 编码
     */
    LATIN_1(3, "Latin-1"),
    ;

    private final Integer value;

    private final String desc;

    public static Optional<AppSmppEncodingEnum> fromValue(Integer value) {
        if (value == null) {
            return Optional.empty();
        }
        for (AppSmppEncodingEnum encoding : values()) {
            if (encoding.value.equals(value)) {
                return Optional.of(encoding);
            }
        }
        return Optional.empty();
    }
}