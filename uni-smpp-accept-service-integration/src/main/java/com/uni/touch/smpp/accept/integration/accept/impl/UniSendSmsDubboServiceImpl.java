package com.uni.touch.smpp.accept.integration.accept.impl;

import com.uni.touch.accept.api.UniSendSmsService;
import com.uni.touch.accept.api.request.UniSendSmsRequest;
import com.uni.touch.accept.api.response.UniSendSmsResponse;
import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.smpp.accept.integration.accept.UniSendSmsDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/07/24
 */
@Slf4j
@Service
public class UniSendSmsDubboServiceImpl implements UniSendSmsDubboService {

    @DubboReference(check = false)
    private UniSendSmsService uniSendSmsService;

    @Override
    public BaseResult<UniSendSmsResponse> sendSms(UniSendSmsRequest request) {
        return uniSendSmsService.sendSms(request);
    }
}
