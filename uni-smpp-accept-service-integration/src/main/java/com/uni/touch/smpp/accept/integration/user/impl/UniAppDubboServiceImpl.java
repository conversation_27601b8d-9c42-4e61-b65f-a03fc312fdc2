package com.uni.touch.smpp.accept.integration.user.impl;

import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.smpp.accept.integration.user.UniAppDubboService;
import com.uni.touch.user.api.UniAppService;
import com.uni.touch.user.api.response.AppInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/07/24
 */
@Slf4j
@Service
public class UniAppDubboServiceImpl implements UniAppDubboService {

    @DubboReference(check = false)
    private UniAppService uniAppService;

    @Override
    public BaseResult<AppInfoResponse> getAppSmppInfoBySmppId(String smppSystemId) {
        return uniAppService.queryAppSmppInfoBySmppId(smppSystemId);
    }
}
