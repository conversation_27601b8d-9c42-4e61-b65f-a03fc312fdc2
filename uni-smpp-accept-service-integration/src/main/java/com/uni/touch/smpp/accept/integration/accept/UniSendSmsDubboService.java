package com.uni.touch.smpp.accept.integration.accept;

import com.uni.touch.accept.api.request.UniSendSmsRequest;
import com.uni.touch.accept.api.response.UniSendSmsResponse;
import com.uni.touch.service.common.result.BaseResult;

/**
 * <AUTHOR>
 * @date 2025/07/24
 */
public interface UniSendSmsDubboService {

    /**
     * 发送短信
     *
     * @param request 短信发送请求参数
     *                private String phoneNumber;
     *                private String senderId;
     *                private String templateId;
     *                private String param;
     *                private String outId;
     *                private String dlrUrl;
     *                private String inboundUrl;
     *                private String content;
     *                private MsgFrameTypeEnum msgFrameType;
     *                private SmsTypeEnum smsType;
     *                private String extend;
     * @return 短信发送结果，包含回执ID和消息条数
     */
    BaseResult<UniSendSmsResponse> sendSms(UniSendSmsRequest request);
}
