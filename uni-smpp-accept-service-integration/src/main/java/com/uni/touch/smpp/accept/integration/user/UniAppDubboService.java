package com.uni.touch.smpp.accept.integration.user;

import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.user.api.response.AppInfoResponse;

/**
 * <AUTHOR>
 * @date 2025/07/24
 */
public interface UniAppDubboService {

    /**
     * 根据SMPP账号获取应用SMPP信息
     *
     * @param smppSystemId SMPP账号
     * @return 应用SMPP信息
     * BaseResult代码实现如下：
     * public class BaseResult<T> {
     * private T data;
     * private String requestId;
     * private boolean success;
     * private String code;
     * private String message;
     * private int retryCount;
     * }
     * AppInfoResponse代码实现如下：
     * public class AppInfoResponse {
     * private String smppSystemId;// SMPP账号
     * private String smppPassword;// SMPP密码
     * private Integer smppEncoding;// DCS0默认编码: 1-GSM-7, 2-ASCII, 3-Latin-1
     * private Integer smppMaxLink;// SMPP最大连接数
     * private Integer smppMaxSubmitSpeedPerLink;// SMPP单连接提交最大速度（次/秒）
     * private Integer smppMaxDlrSpeedPerLink;// SMPP单连接状态报告最大速度（次/秒）
     * private Integer smppMaxSlippingWindowNumber;// SMPP单连接滑动窗口数
     * }
     */
    BaseResult<AppInfoResponse> getAppSmppInfoBySmppId(String smppSystemId);
}
