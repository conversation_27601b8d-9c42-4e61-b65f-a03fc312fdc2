<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.uni.touch</groupId>
    <artifactId>uni-smpp-accept-service</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>uni-smpp-accept-service</name>
    <description>uni-smpp-accept-service</description>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
        <uni.touch.common.version>1.0.2</uni.touch.common.version>
        <uni.sms.service.api.version>1.1.0</uni.sms.service.api.version>
        <uni.user.service.client.version>1.2.1-SNAPSHOT</uni.user.service.client.version>
        <uni.user.service.api.version>1.3.0-SNAPSHOT</uni.user.service.api.version>
        <uni.accept.service.api.version>1.1.0-SNAPSHOT</uni.accept.service.api.version>
    </properties>
    <modules>
        <module>uni-smpp-accept-service-starter</module>
        <module>uni-smpp-accept-service-dal</module>
        <module>uni-smpp-accept-service-integration</module>
        <module>uni-smpp-accept-service-service</module>
        <module>uni-smpp-accept-service-common</module>
        <module>uni-smpp-accept-service-api</module>
        <module>uni-smpp-accept-service-controller</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-controller</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-dal</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-integration</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-smpp-accept-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-sms-service-api</artifactId>
                <version>${uni.sms.service.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-user-service-client</artifactId>
                <version>${uni.user.service.client.version}</version>
            </dependency>

            <!--依赖服务-->
            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-touch-common</artifactId>
                <version>${uni.touch.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-user-service-api</artifactId>
                <version>${uni.user.service.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>uni-accept-service-api</artifactId>
                <version>${uni.accept.service.api.version}</version>
            </dependency>

            <!--spring相关-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.22</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>2.7.8</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>1.3.3</version>
            </dependency>


            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.5.5.Final</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>1.5.5.Final</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.31</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.2.1</version>
            </dependency>

            <dependency>
                <groupId>com.uni.touch</groupId>
                <artifactId>unitouch-spring-boot-starter</artifactId>
                <version>1.0.6</version>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-log4j-context-data-2.17-autoconfigure</artifactId>
                <version>2.11.0-alpha</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>5.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>5.3.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.9</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.26</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.2.0.Final</version>
            </dependency>

            <!-- 动态配置依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>3.1.8</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>3.1.8</version>
            </dependency>
            <!-- 动态配置依赖 -->

        </dependencies>
    </dependencyManagement>


    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>ap-southeast-1</id>
            <properties>
                <spring.profiles.active>ap-southeast-1</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>ap-southeast-1-test</id>
            <properties>
                <spring.profiles.active>ap-southeast-1-test</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <verbose>true</verbose>
                    <fork>true</fork>
                    <compilerArguments>
                        <verbose/>
                        <bootclasspath>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</bootclasspath>
                    </compilerArguments>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>